import type {
  UserData,
  EncryptedData,
  UserInviteResponse,
  UserInvitesFilters,
  UserLogsFilters,
  UserLogEntry,
  InviteAnswer,
  UserFromInviteData,
  ApiKeyResponse,
  UserInviteRequest,
  PaginatedResponse,
  EulaResponse
} from "~/types/global";
import { REPORTS_CLIENT } from "~/services/clients/reports.client";

/**
 * Fetches the current user data.
 */
export const fetchUserData = async (): Promise<UserData> => {
  try {
    const response = await REPORTS_CLIENT.get<UserData>(`/auth/user`);
    console.log("[/auth/user] response.data: ", response.data);
    return response.data;
  } catch (error) {
    console.error("Error fetching user data:", error);
    throw error;
  }
};

export const postVerifier = async (verifier: EncryptedData) => {
  try {
    const response = await REPORTS_CLIENT.post<EncryptedData>(`/verifier`, { verifier });
    const data = response.data;
    return data;
  } catch (error) {
    console.error("Error posting verifier:", error);
    throw error;
  }
};

export const postApiKey = async (apiKey: string): Promise<ApiKeyResponse> => {
  try {
    if (!apiKey?.trim()) {
      throw new Error("API key is required");
    }

    const response = await REPORTS_CLIENT.post<ApiKeyResponse>(`/set_apikey`, {
      apiKey: apiKey.trim(),
    });
    return response.data;
  } catch (error) {
    console.error("Error setting API key:", error);
    throw error;
  }
};

export const postUserInvite = async ({ email_invited, credits_sent, report_types, type_invite }: UserInviteRequest): Promise<UserInviteResponse> => {
  console.log("[/create_invite] email_invited: ", email_invited);
  console.log("[/create_invite] credits_sent: ", credits_sent);
  console.log("[/create_invite] report_types: ", report_types);
  console.log("[/create_invite] type_invite: ", type_invite);
  try {
    if (!email_invited?.trim()) {
      throw new Error("Email is required");
    }
    if (!credits_sent?.trim()) {
      throw new Error("Credits amount is required");
    }
    if (!type_invite?.trim()) {
      throw new Error("Invite type is required");
    }
    if (!report_types || report_types.length === 0) {
      throw new Error("At least one report type is required");
    }

    const payload = {
      email_invited: email_invited.trim(),
      credits_sent: credits_sent.trim(),
      report_types: report_types.filter(type => type?.trim()),
      type_invite: type_invite.trim(),
    };

    console.log("[/create_invite] payload: ", payload);
    const response = await REPORTS_CLIENT.post<UserInviteResponse>(`/create_invite`, payload);
    console.log("[/create_invite] response.data: ", response.data);
    return response.data;
  } catch (error) {
    console.error("Error sending invite:", error);
    throw error;
  }
};

export const getUserInvites = async (filters: UserInvitesFilters = {}): Promise<PaginatedResponse<UserInviteResponse>> => {
  const {
    status_invite = "",
    type_invite = "",
    specific_date = "",
    order = "desc",
    column_order = "sent_at",
    limit = 10,
    page = 1,
    search_email = ""
  } = filters;
  try {
    console.log("[/get_organization_invite] filters: ", filters);
    const queryParams = new URLSearchParams();

    if (status_invite?.trim()) {
      queryParams.append("status_invite", status_invite.trim());
    }
    if (type_invite?.trim()) {
      queryParams.append("type_invite", type_invite.trim());
    }
    if (specific_date?.trim()) {
      queryParams.append("specific_date", specific_date.trim());
    }
    if (search_email?.trim()) {
      queryParams.append("search_email", search_email.trim());
    }

    queryParams.append("order", order || "desc");
    queryParams.append("column_order", column_order || "sent_at");
    queryParams.append("limit", limit.toString());
    queryParams.append("page", page.toString());

    const response = await REPORTS_CLIENT.get<PaginatedResponse<UserInviteResponse>>(`/get_organization_invite?${queryParams.toString()}`);
    console.log("[/get_organization_invite] response.data: ", response.data);
    return response.data;
  } catch (error) {
    console.error("Error getting invites:", error);
    throw error;
  }
};

export const getUserLogs = async (filters: UserLogsFilters = {}): Promise<PaginatedResponse<UserLogEntry>> => {
  const {
    is_user = true,
    column_order = "created_at",
    order = "desc",
    report_type = "",
    limit = 10,
    page = 1,
    created_start_at = "",
    created_end_at = ""
  } = filters;
  try {
    console.log("[/get-logs] filters: ", filters);
    const queryParams = new URLSearchParams();

    queryParams.append("is_user", is_user.toString());
    queryParams.append("order", order || "desc");
    queryParams.append("column_order", column_order || "created_at");
    queryParams.append("limit", limit.toString());
    queryParams.append("page", page.toString());

    if (report_type?.trim()) {
      queryParams.append("report_type", report_type.trim());
    }
    if (created_start_at?.trim()) {
      queryParams.append("created_start_at", created_start_at.trim());
    }
    if (created_end_at?.trim()) {
      queryParams.append("created_end_at", created_end_at.trim());
    }

    const response = await REPORTS_CLIENT.get<PaginatedResponse<UserLogEntry>>(`/get-logs?${queryParams.toString()}`);
    console.log("[/get-logs] response.data: ", response.data);
    return response.data;
  } catch (error) {
    console.error("Error getting user logs:", error);
    throw error;
  }
};

export const getUserFromInvite = async (invite_id: string): Promise<UserFromInviteData> => {
  try {
    console.log("[/get_user_data_from_invite] invite_id: ", invite_id);
    if (!invite_id?.trim()) {
      throw new Error("Invite ID is required");
    }

    const response = await REPORTS_CLIENT.get<UserFromInviteData>(`/get_user_data_from_invite/${invite_id.trim()}`);
    console.log("[/get_user_data_from_invite] response.data: ", response.data);
    return response.data;
  } catch (error) {
    console.error("Error getting user from invite:", error);
    throw error;
  }
};

export const editInvitedUser = async (user_id: string, report_types: any, role: string, credits_monthly: number) => {
  try {
    console.log("[/update_invited_user] user_id: ", user_id);
    console.log("[/update_invited_user] report_types: ", report_types);
    console.log("[/update_invited_user] role: ", role);
    console.log("[/update_invited_user] credits_monthly: ", credits_monthly);
    const response = await REPORTS_CLIENT.put(
      `/update_invited_user/${user_id}`, { report_types, role, credits_monthly });
    return response.data;
  } catch (error) {
    console.error("Error editing invited user:", error);
    throw error;
  }
};

export const getUserInvite = async (status_invite: string = "enviado", type_invite: string = ""): Promise<UserInviteResponse[]> => {
  try {
    const queryParams = new URLSearchParams();

    if (status_invite?.trim()) {
      queryParams.append("status_invite", status_invite.trim());
    }
    if (type_invite?.trim()) {
      queryParams.append("type_invite", type_invite.trim());
    }

    const response = await REPORTS_CLIENT.get<UserInviteResponse[]>(`/get_user_invite?${queryParams.toString()}`);
    console.log("[/get_user_invite] response.data: ", response.data);
    return response.data;
  } catch (error) {
    console.error("Error getting user invites:", error);
    throw error;
  }
};

export const postAnswerInvite = async (invite_answer: InviteAnswer): Promise<ApiKeyResponse> => {
  try {
    if (typeof invite_answer.accept_invite !== 'boolean') {
      throw new Error("Accept invite decision is required");
    }
    if (!invite_answer.invite_id?.trim()) {
      throw new Error("Invite ID is required");
    }

    const payload = {
      accept_invite: invite_answer.accept_invite,
      invite_id: invite_answer.invite_id.trim(),
    };

    console.log("[/answer_invite] payload: ", payload);
    const response = await REPORTS_CLIENT.post<ApiKeyResponse>(`/answer_invite`, payload);
    console.log("[/answer_invite] response.data: ", response.data);
    return response.data;
  } catch (error) {
    console.error("Error answering invite:", error);
    throw error;
  }
};

export const cancelSentInvite = async (invite_id: string): Promise<ApiKeyResponse> => {
  try {
    console.log("[/cancel_invite] invite_id: ", invite_id);
    if (!invite_id?.trim()) {
      throw new Error("Invite ID is required");
    }

    console.log("[/cancel_invite] invite_id: ", invite_id.trim());
    const response = await REPORTS_CLIENT.put<ApiKeyResponse>(`/cancel_invite/${encodeURIComponent(invite_id.trim())}`, {});
    console.log("[/cancel_invite] response.data: ", response.data);
    return response.data;
  } catch (error) {
    console.error("Error canceling invite:", error);
    throw error;
  }
};

export const removeUserFromOrganization = async (invite_id: string): Promise<ApiKeyResponse> => {
  try {
    console.log("[/remove_user_from_organization] invite_id: ", invite_id);
    if (!invite_id?.trim()) {
      throw new Error("Invite ID is required");
    }

    const response = await REPORTS_CLIENT.post<ApiKeyResponse>(`/leave_organization`, {
      invite_id: invite_id.trim(),
    });
    console.log("[/remove_user_from_organization] response.data: ", response.data);
    return response.data;
  } catch (error) {
    console.error("Error removing user from organization:", error);
    throw error;
  }
};

export const getInviteDetails = async (invite_id: string): Promise<UserInviteResponse | UserInviteResponse[]> => {
  try {
    console.log("[/get_invite_details] invite_id: ", invite_id);
    if (!invite_id?.trim()) {
      throw new Error("Invite ID is required");
    }

    const response = await REPORTS_CLIENT.get<UserInviteResponse[] | UserInviteResponse>(`/get_invite_details/${invite_id?.trim()}`);
    console.log("[/get_invite_details] response.data: ", response.data);

    // API retorna um array com um único elemento
    if (Array.isArray(response.data) && response.data.length > 0) {
      return response.data[0];
    }

    return response.data;
  } catch (error) {
    console.error("Error getting invite details:", error);
    throw error;
  }
};

export const updateUseSnapLogo = async (print_snap_logo: boolean): Promise<{ message: string }> => {
  try {
    console.log("[/print_snap_logo] print_snap_logo: ", print_snap_logo);
    const response = await REPORTS_CLIENT.patch(`/print_snap_logo`, {
      print_snap_logo,
    });
    return response.data as { message: string };
  } catch (error) {
    console.error("Error updating print snap logo:", error);
    throw error;
  }
};

export const getEula = async (): Promise<EulaResponse> => {
  try {
    /* TODO - remover comentário e mock quando a api estiver pronta */
    // const response = await REPORTS_CLIENT.get<EulaResponse>(`/get_eula`);
    // return response.data;

    const fetchedData = await fetch('/mock_termo_aceite.json');
    const data = await fetchedData.json();
    return data;
  } catch (error) {
    console.error("Error getting eula:", error);
    throw error;
  }
};

export const acceptTerms = async (): Promise<{ message: string }> => {
  try { 
    const response = await REPORTS_CLIENT.patch(`/accept_terms`, {});
    return response.data as { message: string };
  } catch (error) {
    console.error("Error accepting terms:", error);
    throw error;
  }
};
