#!/bin/bash
set -e

echo "🚀 Keycloak Entrypoint Starting..."
export KC_HOSTNAME_STRICT=false
export KC_HOSTNAME_STRICT_HTTPS=false

# Set admin credentials
if [ -f "/run/secrets/keycloak_admin_user" ]; then
    export KEYCLOAK_ADMIN=$(cat /run/secrets/keycloak_admin_user)
else
    export KEYCLOAK_ADMIN=admin
fi

if [ -f "/run/secrets/keycloak_admin_password" ]; then
    export KEYCLOAK_ADMIN_PASSWORD=$(cat /run/secrets/keycloak_admin_password)
else
    export KEYCLOAK_ADMIN_PASSWORD=admin123
fi

echo "ℹ️ Starting Keycloak with user: $KEYCLOAK_ADMIN"

# 🏃 Start role assignment script in background
/opt/keycloak/assign-roles.sh &

# 🏁 Start Keycloak in foreground
exec /opt/keycloak/bin/kc.sh "$@"
