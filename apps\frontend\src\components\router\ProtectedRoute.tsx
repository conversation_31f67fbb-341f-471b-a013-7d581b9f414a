import { Navigate, Outlet, useLocation } from "react-router";
import { useEffect, useState } from "react";
import { useUserCRUD } from "~/hooks/useUserCRUD";
import { useUserActions, useUserHasAcceptedTerms } from "~/store/userStore";
import { DialogContainer } from "../DialogContainer";
import { PendingReportSockets } from "../PendingReportSockets";
import { Loading, ModalInstance } from "@snap/design-system";
import { useDialogActions } from "~/store/dialogStore";
import { EulaDialog } from "~/containers/report/EulaDialog";
import { Check } from "lucide-react";

export default function ProtectedRoute() {
  const location = useLocation();
  const { userQueryUser } = useUserCRUD();
  const { data: userData, isLoading, isError } = userQueryUser;
  const { setUser, setUserSalt, setHasAcceptedTerms } = useUserActions();
  const { openDialog } = useDialogActions();
  const hasAcceptedTerms = useUserHasAcceptedTerms();
  const [hasShownDialog, setHasShownDialog] = useState(false);

  useEffect(() => {
    if (userData) {
      setUser({ ...userData });
      setUserSalt(userData?.salt || null);
      setHasAcceptedTerms(!!userData?.accept_terms);
    }
  }, [userData, setUser, setUserSalt, setHasAcceptedTerms]);

  /* TODO - remover comentário quando a api estiver pronta */
  // useEffect(() => {
  //   if (userData && !userData.accept_terms && !hasShownDialog) {
  //     setHasShownDialog(true);
  //     openDialog({
  //       title: "Termos de Uso",
  //       icon: <Check />,
  //       content: <EulaDialog.Content />,
  //       footer: <EulaDialog.Footer />,
  //       className: "max-w-4xl",
  //       preventOutsideClose: true,
  //     });
  //   }
  // }, [userData, hasShownDialog, openDialog]);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-screen w-full">
        <Loading size="lg" />
      </div>
    );
  }

  if (isError || !userData) {
    return <Navigate to="/login" replace state={{ from: location }} />;
  }

  /* TODO - remover comentário quando a api estiver pronta */
  // if (!hasAcceptedTerms) {
  //   return (
  //     <div className="flex items-center justify-center h-screen w-full">
  //       <ModalInstance />
  //       <DialogContainer />
  //       <Loading size="lg" />
  //     </div>
  //   );
  // }

  return (
    <>
      <ModalInstance />
      <PendingReportSockets />
      <DialogContainer />
      <Outlet />
    </>
  );
}
