import { DisplayableItem, ItemType } from "root/domain/entities/base.model";
import { ReportModel } from "root/domain/entities/report.model";
import { FolderModel } from "root/domain/entities/folder.model";
import ReportCard from "./ReportCard";
import FolderCard from "./FolderCard";

interface CardFactoryProps {
  item: DisplayableItem;
  onAccess?: () => void;
  onRetry?: () => void;
  onDownloadPDF?: (reportId: string) => void;
  isDownloadingPDF?: boolean;
}

export function CardFactory({ item, onAccess, onRetry, onDownloadPDF, isDownloadingPDF }: CardFactoryProps) {
  if (item.type === ItemType.REPORT) {
    return <ReportCard report={item as ReportModel} onAccess={onAccess} onRetry={onRetry} onDownloadPDF={onDownloadPDF} isDownloadingPDF={isDownloadingPDF} />;
  }
  
  if (item.type === ItemType.FOLDER) {
    return <FolderCard folder={item as FolderModel} onAccess={onAccess} />;
  }
  
  return null;
}