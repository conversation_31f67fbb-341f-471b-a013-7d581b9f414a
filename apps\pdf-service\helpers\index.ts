import { TRANSLATED_LABELS, SOURCE_LABELS, PARSED_VALUES } from '../config/constants';
import path from 'path';
import fs from 'fs';

export const translatePropToLabel = (propName: string): string => {
  if (!propName || typeof propName !== "string") return String(propName);

  const normalizedInput = propName.toLowerCase();

  for (const key in TRANSLATED_LABELS) {
    if (key.toLowerCase() === normalizedInput) {
      return TRANSLATED_LABELS[key as keyof typeof TRANSLATED_LABELS];
    }
  }

  return propName;
};

export const translateSource = (source: string): string => {
  if (!source || typeof source !== "string") return String(source);
  return SOURCE_LABELS[source] || source;
};

export const getInitials = (name: string | undefined): string => {
  if (!name) return "N/A";

  const nameParts = name.trim().split(" ").filter(Boolean);

  if (nameParts.length === 1) {
    return nameParts[0][0].toUpperCase();
  } else if (nameParts.length > 1) {
    const firstInitial = nameParts[0][0];
    const lastInitial = nameParts[nameParts.length - 1][0];
    return `${firstInitial}${lastInitial}`.toUpperCase();
  }

  return "N/A";
};

export const getFieldLabel = (key: string, value: any): string => {
  const raw = typeof value === 'object' && value?.label
    ? value.label
    : key;

  return translatePropToLabel(raw);
};

export const getFieldValue = (value: any): any => {
  return typeof value === 'object' && value?.value !== undefined
    ? value.value
    : value;
};

export const isValidUrl = (url: string | undefined | null) => {
  if (!url) return false;
  try {
    new URL(url);
    return true;
  } catch (error) {
    return false;
  }
};

export const isBase64Image = (str: string) => {
  if (!str) return false;
  try {
    if (str.startsWith('data:image')) {
      return true;
    }

    if (str.startsWith('/9j/') || str.startsWith('/9k/') ||
      str.startsWith('iVBORw0KGgo') ||
      str.startsWith('R0lGODlh') || str.startsWith('R0lGODdh') ||
      str.startsWith('UklGR')) {
      return true;
    }

    if (str.length < 10000) {
      return btoa(atob(str)) == str;
    }

    return false;
  } catch (err) {
    return false;
  }
};

export const formatImageSrc = (value: string): string => {
  if (!value) return value;

  if (isValidUrl(value) || value.startsWith('data:')) {
    return value;
  }

  if (isBase64Image(value)) {
    if (value.startsWith('/9j/') || value.startsWith('/9k/')) {
      return `data:image/jpeg;base64,${value}`;
    } else if (value.startsWith('iVBORw0KGgo')) {
      return `data:image/png;base64,${value}`;
    } else if (value.startsWith('R0lGODlh') || value.startsWith('R0lGODdh')) {
      return `data:image/gif;base64,${value}`;
    } else if (value.startsWith('UklGR')) {
      return `data:image/webp;base64,${value}`;
    } else {
      return `data:image/jpeg;base64,${value}`;
    }
  }

  return value;
};

export const getSingular = (key: string): string => {
  if (!key) return "";

  const singularMap: Record<string, string> = {
    aplicativos: "aplicativo",
    enderecos: "endereço",
    veiculos: "veículo",
    diarios: "diário",
    oficiais: "oficial",
    sources: "fonte",
    socios: "sócio",
    movimentacoes: "movimentação",
    emails: "email",
    telefones: "telefone",
    pessoas: "pessoa",
    empresas: "empresa",
    processos: "processo",
  };

  const normalizedKey = key.toLowerCase();
  return singularMap[normalizedKey] || key;
};

export const getPlural = (key: string): string => {
  if (!key) return "";

  const pluralMap: Record<string, string> = {
    aplicativo: "aplicativos",
    endereco: "endereços",
    veiculo: "veículos",
    diario: "diários",
    oficial: "oficiais",
    fonte: "fontes",
    socio: "sócios",
    movimentacao: "movimentações",
    email: "emails",
    telefone: "telefones",
    pessoa: "pessoas",
    empresa: "empresas",
    processo: "processos",
  };

  const normalizedKey = key.toLowerCase();
  return pluralMap[normalizedKey] || key;
};

export const parseValue = (value: string): string => {
  if (!value || typeof value !== "string") return value;

  const normalizedInput = value.toLowerCase();

  for (const key in PARSED_VALUES) {
    if (key.toLowerCase() === normalizedInput) {
      return PARSED_VALUES[key as keyof typeof PARSED_VALUES];
    }
  }

  const legacyValues: Record<string, string> = {
    "sim": "Sim",
    "nao": "Não",
    "true": "Sim",
    "false": "Não",
    "masculino": "Masculino",
    "feminino": "Feminino",
    "m": "Masculino",
    "f": "Feminino"
  };

  return legacyValues[normalizedInput] || value;
};

export const getImageAsDataUrl = (imagePath: string): string => {
  try {
    console.log(`Attempting to load image: ${imagePath}`);
    
    if (!fs.existsSync(imagePath)) {
      console.error(`Image file does not exist: ${imagePath}`);
      return '';
    }
    
    const stats = fs.statSync(imagePath);
    console.log(`Image file size: ${stats.size} bytes`);
    
    const imageBuffer = fs.readFileSync(imagePath);
    const ext = path.extname(imagePath).toLowerCase();
    const mimeType = ext === '.png' ? 'image/png' : ext === '.jpg' || ext === '.jpeg' ? 'image/jpeg' : 'image/png';
    const dataUrl = `data:${mimeType};base64,${imageBuffer.toString('base64')}`;
    
    console.log(`Successfully loaded image: ${imagePath} (${dataUrl.length} chars)`);
    return dataUrl;
  } catch (error) {
    console.error(`Failed to load image: ${imagePath}`, error);
  }
  return '';
};

export const getImageAsDataUrlAsync = async (imagePath: string): Promise<string> => {
  try {
    console.log(`Attempting to load image: ${imagePath}`);
    
    if (isValidUrl(imagePath)) {
      const response = await fetch(imagePath);
      if (!response.ok) {
        console.error(`Failed to fetch image from URL: ${imagePath}`);
        return '';
      }
      const arrayBuffer = await response.arrayBuffer();
      const buffer = Buffer.from(arrayBuffer);
      const ext = path.extname(new URL(imagePath).pathname).toLowerCase() || '.png';
      const mimeType = ext === '.png' ? 'image/png' : ext === '.jpg' || ext === '.jpeg' ? 'image/jpeg' : ext === '.svg' ? 'image/svg+xml' : 'image/png';
      const dataUrl = `data:${mimeType};base64,${buffer.toString('base64')}`;
      console.log(`Successfully loaded image from URL: ${imagePath} (${dataUrl.length} chars)`);
      return dataUrl;
    }
    
    // fallback pra tentar carregar como arquivo local
    return getImageAsDataUrl(imagePath);
  } catch (error) {
    console.error(`Failed to load image: ${imagePath}`, error);
  }
  return '';
};

export const getFontAsDataUrl = (fontPath: string): string => {
  try {
    if (fs.existsSync(fontPath)) {
      const fontBuffer = fs.readFileSync(fontPath);
      const ext = path.extname(fontPath).toLowerCase();
      const mimeType = ext === '.otf' ? 'font/otf' : ext === '.ttf' ? 'font/ttf' : 'font/woff2';
      return `data:${mimeType};base64,${fontBuffer.toString('base64')}`;
    }
  } catch (error) {
    console.warn(`Failed to load font: ${fontPath}`, error);
  }
  return '';
};

export const getAgeFromBirthDate = (birthDate: string) => {
  if (!birthDate) return "";

  const today = new Date();
  const dateBirthday = new Date(birthDate);

  let age = today.getFullYear() - dateBirthday.getFullYear();
  const monthDifference = today.getMonth() - dateBirthday.getMonth();

  // Adjust age if the birthday hasn't occurred yet this year
  if (monthDifference < 0 || (monthDifference === 0 && today.getDate() < dateBirthday.getDate())) {
    age--;
  }
  return age;
}

/**
 * Determines if a report subject is a company or person based on report type and subject attributes
 *
 * @param reportType - The type of report (cpf, cnpj, telefone, email)
 * @param subjectAge - The subject's age/date value
 * @param subjectSex - The subject's sex (optional)
 * @param subjectMotherName - The subject's mother name (optional)
 * @returns true if the subject is a company, false if it's a person
 */
export const isReportSubjectCompany = (
  reportType: string,
  subjectAge?: string | number | null,
  subjectSex?: string | null,
  subjectMotherName?: string | null
): boolean => {
  if (reportType === 'cpf') {
    return false;
  }

  if (reportType === 'cnpj') {
    return true;
  }

  if (reportType === 'telefone' || reportType === 'email') {
    const hasSex = subjectSex && subjectSex.trim();
    const hasMotherName = subjectMotherName && subjectMotherName.trim();

    if (hasSex || hasMotherName) {
      return false;
    } else if (subjectAge != null && subjectAge !== "") {
      return true;
    }
  }

  return false;
};