
import { Button, Loading, Text } from "@snap/design-system";
import { useUserCRUD } from "~/hooks/useUserCRUD";
import { useDialogActions } from "~/store/dialogStore";
import { useState, useCallback } from "react";
import { Checkbox } from "~/components/ui/checkbox";
import { AiOutlineLoading3Quarters } from "react-icons/ai";
import { Check, X, LogOut, AlertTriangle } from "lucide-react";
import { useAuth } from "~/hooks/useAuth";

export function EulaDialogContent() {
  const { eulaQuery } = useUserCRUD();
  const { data: eulaData, isLoading } = eulaQuery;

  return (
    <div className="max-h-96 overflow-y-auto">
      <Text
        variant="body-lg"
        className="mb-5 font-bold"
      >
        Para utilizar esta plataforma, você deve estar plenamente de acordo com os nossos termos de uso, abaixo relacionados:
      </Text>
      {isLoading ? (
        <div className="flex items-center justify-center p-8">
          <Loading size="lg" />
        </div>
      ) :
        <div
          className="text-sm [&_ul]:list-disc [&_ul]:ml-6 [&_li]:mb-2 [&_p]:mb-5 [&_h3]:mb-5 [&_h3]:font-bold"
          dangerouslySetInnerHTML={{ __html: eulaData?.content || "Carregando termos..." }}
        />
      }
    </div>
  );
}

export function EulaDialogFooter() {
  const { acceptTermsMutation, eulaQuery } = useUserCRUD();
  const { closeDialog, openDialog } = useDialogActions();
  const [isChecked, setIsChecked] = useState(false);
  const {
    logoutMutation: { mutateAsync: logout },
  } = useAuth();
  const { error, isFetching } = eulaQuery;

  const handleAccept = async () => {
    try {
      await acceptTermsMutation.mutateAsync();
      closeDialog();
    } catch (error) {
      console.error("Error accepting terms:", error);
    }
  };

  const handleDecline = () => {
    const handleConfirmDecline = () => {
      logout();
    };

    const handleCancelDecline = () => {
      openDialog({
        title: "Termos de Uso",
        icon: <AlertTriangle />,
        content: <EulaDialogContent />,
        footer: <EulaDialogFooter />,
        className: "max-w-4xl",
      });
    };

    openDialog({
      title: "Confirmação de Recusa",
      icon: <AlertTriangle />,
      content: (
        <EulaDeclineConfirmationDialog.Content
          onConfirm={handleConfirmDecline}
          onCancel={handleCancelDecline}
        />
      ),
      footer: (
        <EulaDeclineConfirmationDialog.Footer
          onConfirm={handleConfirmDecline}
          onCancel={handleCancelDecline}
        />
      ),
      className: "max-w-lg",
    });
  };

  const handleCheckboxChange = (checked: boolean) => {
    setIsChecked(checked);
  };

  const handleKeyDown = useCallback((event: React.KeyboardEvent) => {
    if (event.key === 'Enter') {
      event.preventDefault();
      setIsChecked(prev => !prev);
    }
  }, []);

  if (error) {
    console.error("Error fetching EULA:", error);
    handleDecline();
  }

  return (
    <div className="flex flex-col gap-4 w-full">
      <div className="flex items-center gap-2">
        <Checkbox
          id="accept-terms"
          checked={isChecked}
          onCheckedChange={handleCheckboxChange}
          onKeyDown={handleKeyDown}
          disabled={isFetching}
          className="border-foreground data-[state=checked]:bg-foreground rounded-none cursor-pointer"
        />
        <label
          htmlFor="accept-terms"
          className="text-sm cursor-pointer"
          onKeyDown={handleKeyDown}
          tabIndex={0}
        >
          Declaro que li e concordo com os termos acima relacionados.
        </label>
      </div>
      <div className="flex gap-3">
        <Button
          onClick={handleDecline}
          icon={<X size={16} />}
          iconPosition="right"
        >
          Recusar
        </Button>
        <Button
          onClick={handleAccept}
          className="uppercase !bg-foreground !text-background !font-bold disabled:opacity-50"
          icon={acceptTermsMutation.isPending ? <AiOutlineLoading3Quarters size={16} /> : <Check size={16} />}
          iconPosition="right"
          disabled={!isChecked || acceptTermsMutation.isPending}
        >
          Aceitar
        </Button>
      </div>
    </div>
  );
}

interface EulaDeclineConfirmationProps {
  onConfirm?: () => void;
  onCancel?: () => void;
}

interface EulaDeclineConfirmationFooterProps {
  onConfirm?: () => void;
  onCancel?: () => void;
}

function EulaDeclineConfirmation({ }: EulaDeclineConfirmationProps) {
  return (
    <div className="space-y-4">
      <div className="flex items-center gap-3 p-4 bg-accent/10 border border-accent rounded-sm">
        <LogOut className="size-5 text-white flex-shrink-0" />
        <div>
          <Text variant="body-md" className="font-semibold">
            Você confirma a recusa dos termos de uso?
          </Text>
          <Text className="mt-1">
            Ao recusar os termos, você não poderá acessar a plataforma e suas funcionalidades, sendo redirecionado para a página de login.
          </Text>
        </div>
      </div>
    </div>
  );
}

function EulaDeclineConfirmationFooter({ onConfirm, onCancel }: EulaDeclineConfirmationFooterProps) {
  return (
    <div className="flex gap-3 justify-end">
      <Button
        onClick={onCancel}
        className="min-w-[100px] uppercase !bg-transparent">
        Voltar
      </Button>
      <Button
        onClick={onConfirm}
        className="min-w-[100px] uppercase !bg-foreground !text-background !font-bold hover:opacity-80 transition-opacity"
        icon={<Check size={16} />}
        iconPosition="right"
      >
        Confirmar
      </Button>
    </div>
  );
}

export const EulaDeclineConfirmationDialog = {
  Content: EulaDeclineConfirmation,
  Footer: EulaDeclineConfirmationFooter
};

export const EulaDialog = {
  Content: EulaDialogContent,
  Footer: EulaDialogFooter,
};