import logging
import math
from sqlalchemy import update, delete, select, and_, distinct, asc, desc, func
from sqlalchemy.dialects.postgresql import insert
from sqlalchemy.ext.asyncio import AsyncSession
from datetime import datetime, timezone
from typing import List
from uuid import uuid4
from services.base_service import BaseService
from services.organization_users_service import OrganizationUsersService

from models.folder_model import Folder
from models.report_model import UserReports
from models.folder_hmac import FolderHmac
from schemas.folder_schema import FolderCreate, FolderId, FolderMove, FolderRename
from core.constants import DefaultPageLogs
from schemas.user_schema import PaginatedResponse, PaginationMetadata
from exceptions.base_exceptions import NotFoundError
import re


logger = logging.getLogger(__name__)

class FolderService(BaseService):

    def __init__(self, db: AsyncSession, user_id: str) -> None:
        super().__init__(db)
        self.user_id=user_id


    async def update_parent_folders_modified_at(self, folder_path: str) -> None:
        """Extract folder IDs from folder_path and update their modified_at to current time."""
        if not folder_path:
            logging.info("[update_parent_folders_modified_at] No folder_path provided, skipping update")
            return

        try:
            logging.info("[update_parent_folders_modified_at] Called with folder_path: '%s' (raw input)", folder_path)
            # Extract folder IDs from the path
            # Remove leading and trailing slashes, then split by '/'
            path_clean = folder_path.strip('/')
            logging.info("[update_parent_folders_modified_at] Cleaned path: '%s'", path_clean)
            if not path_clean:
                logging.info("[update_parent_folders_modified_at] Empty path after cleaning, skipping update")
                return

            folder_ids = path_clean.split('/')
            logging.info("[update_parent_folders_modified_at] Split folder_ids: %s", folder_ids)
            # Filter out empty strings and validate UUID format
            valid_folder_ids = []
            uuid_pattern = re.compile(r'^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$', re.IGNORECASE)

            for folder_id in folder_ids:
                if folder_id:
                    if uuid_pattern.match(folder_id):
                        logging.info("[update_parent_folders_modified_at] Valid folder_id found: %s", folder_id)
                        valid_folder_ids.append(folder_id)
                    else:
                        logging.info("[update_parent_folders_modified_at] Invalid folder_id format: %s", folder_id)
                else:
                    logging.info("[update_parent_folders_modified_at] Skipping empty folder_id in split list")

            if not valid_folder_ids:
                logging.info("[update_parent_folders_modified_at] No valid folder IDs found in path: %s", folder_path)
                return

            logging.info("[update_parent_folders_modified_at] Updating modified_at for folder IDs: %s (user_id: %s)", valid_folder_ids, self.user_id)

            # Update modified_at for all parent folders
            result = await self.db.execute(
                update(Folder)
                .where(Folder.folder_id.in_(valid_folder_ids))
                .where(Folder.user_folder_id == self.user_id)
                .values(modified_at=datetime.now(timezone.utc))
            )
            logging.info("[update_parent_folders_modified_at] DB update result: %s", result)

            await self.db.commit()
            logging.info("[update_parent_folders_modified_at] DB commit successful for folder IDs: %s", valid_folder_ids)

        except Exception as e:
            logging.error("[update_parent_folders_modified_at] Error updating parent folders modified_at: %s", e)
            await self.db.rollback()
            raise


    async def create_folder(self, folder_in: FolderCreate) -> FolderId:
        logging.info(f"[create_folder] Called with folder_in={folder_in}")
        
        # Generate a new UUID for the folder
        new_folder_id = str(uuid4())
        logging.info(f"[create_folder] Generated new folder_id: {new_folder_id}")
        
        # Fetch parent folder if parent_folder_id is provided
        parent_folder = None
        if folder_in.parent_folder_id:
            parent_folder = await self.db.get(Folder, folder_in.parent_folder_id)
            logging.info(f"[create_folder] Parent folder fetched: {parent_folder}")

        # Calculate folder_path and depth_level
        if parent_folder:
            folder_path = f"{parent_folder.folder_path}{new_folder_id}/"
            depth_level = parent_folder.depth_level + 1
            logging.info(f"[create_folder] Calculated folder_path={folder_path}, depth_level={depth_level}")
        else:
            folder_path = f"/{new_folder_id}/"
            depth_level = 1
            logging.info(f"[create_folder] Root folder: folder_path={folder_path}, depth_level={depth_level}")

        organization_users_service = OrganizationUsersService(db=self.db, user_id=self.user_id)
        org_data = await organization_users_service.get_organization_user()
        organization_id = None
        if org_data:
            organization_id = org_data.organization_id
            logger.info(f"[create_folder] Active organization found for user: {self.user_id}")



        # Create the folder
        folder = Folder(
            folder_id=new_folder_id,
            folder_name=folder_in.folder_name,
            parent_folder_id=folder_in.parent_folder_id,
            folder_path=folder_path,
            depth_level=depth_level,
            created_at=datetime.now(timezone.utc),
            modified_at=datetime.now(timezone.utc),
            user_folder_id=self.user_id,
            organization_id=organization_id
        )
        logging.info(f"[create_folder] Creating folder: {folder}")
        self.db.add(folder)
        await self.db.commit()
        await self.db.refresh(folder)
        logging.info(f"[create_folder] Folder created: {folder}")

        # Update user_reports to point to this folder
        if folder_in.user_reports_id_list:
            for user_report_id in folder_in.user_reports_id_list:
                logging.info(f"[create_folder] Updating UserReports {user_report_id} to folder_id={folder.folder_id}")
                await self.db.execute(
                    update(UserReports)
                    .where(UserReports.user_reports_id == user_report_id)
                    .values(folder_id=folder.folder_id)
                )

            await self.db.commit()
            logging.info(f"[create_folder] UserReports updated and committed.")

        # Insert HMACs if provided
        if folder_in.hmac_folder_name:
            await self.insert_hmacs_folder(folder_in.hmac_folder_name, folder.folder_id)
            logging.info(f"[create_folder] HMACs inserted for folder_id: {folder.folder_id}")

        return FolderId(folder_id=folder.folder_id)


    async def insert_hmacs_folder(self, hmac: list[str], folder_id: str):
        """Insert HMACs for a folder"""
        if not folder_id:
            logger.error("[insert_hmacs_folder] No folder_id provided")
            raise ValueError("folder_id is required")

        try:
            logger.info("[insert_hmacs_folder] Inserting hmacs for folder_id: %s", folder_id)

            rows_to_insert = []
            for hmac_value in hmac:
                    rows_to_insert.append({
                        'hmac': hmac_value,
                        'folder_id': folder_id
                    })

            if rows_to_insert:
                stmt = insert(FolderHmac).values(rows_to_insert).on_conflict_do_nothing()
                await self.db.execute(stmt)
                await self.db.commit()

            logger.info("[insert_hmacs_folder] Successfully inserted hmacs for folder_id: %s", folder_id)

        except Exception as e:
            logger.error("[insert_hmacs_folder] Error inserting hmacs for folder_id %s: %s", folder_id, e)
            await self.db.rollback()
            raise


    async def rename_folder(self, folder_changes: FolderRename) -> Folder:
        logging.info(f"[rename_folder] Called with folder_changes={folder_changes}")

        modified_at = datetime.now(timezone.utc)
        result = await self.db.execute(
            update(Folder)
            .where(Folder.folder_id == folder_changes.folder_id)
            .where(Folder.user_folder_id == self.user_id)
            .values(folder_name=folder_changes.folder_name)
            .values(modified_at=modified_at)
            .returning(Folder)
        )
        await self.db.commit()
        folder = result.scalars().first()
        logging.info(f"[rename_folder] Folder updated: {folder}")
        await self.delete_hmacs_folder(folder_changes.folder_id)
        logging.info(f"[rename_folder] HMACs deleted for folder_id: {folder_changes.folder_id}")


        await self.insert_hmacs_folder(folder_changes.hmac_folder_name, folder_changes.folder_id)

        logging.info(f"[rename_folder] HMACs inserted for folder_id: {folder_changes.folder_id}")

        await self.update_parent_folders_modified_at(folder.folder_path)

        logging.info(f"[rename_folder] Folder {folder_changes.folder_id} renamed to {folder_changes.folder_name}")


    async def move_file_between_folders(self, folder_move: FolderMove):
        logging.info(f"[move_file_between_folders] Called with src_folder_id={folder_move.src_folder_id}, dest_folder_id={folder_move.dest_folder_id}, user_reports_id_list={folder_move.user_reports_id_list}, folder_id_list={folder_move.folder_id_list}")
        # 1. Get folder_path and depth_level from src_folder_id
        folder_path = None
        depth_level = 0
        src_folder_path=''
        src_depth_level=0
        if folder_move.src_folder_id:
            src_folder = await self.db.get(Folder, folder_move.src_folder_id)
            if src_folder:
                src_folder_path = src_folder.folder_path
                src_depth_level = src_folder.depth_level
                logging.info(f"[move_file_between_folders] src_folder found: folder_path={src_folder_path}, depth_level={src_depth_level}")
            else:
                logging.warning(f"[move_file_between_folders] src_folder_id {folder_move.src_folder_id} not found.")
        else:
            logging.info("[move_file_between_folders] src_folder_id is None. Setting folder_path=None, depth_level=0.")

        # 2. Update all user_reports_id in FolderMove.user_reports_id_list
        if folder_move.user_reports_id_list:
            logging.info(f"[move_file_between_folders] Updating UserReports: {folder_move.user_reports_id_list} to folder_id={folder_move.dest_folder_id}")
            await self.db.execute(
                update(UserReports)
                .where(UserReports.user_reports_id.in_(folder_move.user_reports_id_list))
                .values(folder_id=folder_move.dest_folder_id)
            )
        else:
            logging.info("[move_file_between_folders] No user_reports_id_list provided.")

        dest_folder_path = ''
        dest_depth_level = 0
        if folder_move.dest_folder_id:
            dest_folder = await self.db.get(Folder, folder_move.dest_folder_id)
            if dest_folder:
                dest_folder_path = dest_folder.folder_path
                dest_depth_level = dest_folder.depth_level
                logging.info(f"[move_file_between_folders] dest_folder found: folder_path={dest_folder_path}, depth_level={dest_depth_level}")
            else:
                logging.warning(f"[move_file_between_folders] dest_folder_id {folder_move.dest_folder_id} not found.")
        else:
            logging.info("[move_file_between_folders] dest_folder_id is None. Setting folder_path=None, depth_level=0.")

        depth_level_diference = dest_depth_level - src_depth_level

        # 3. Update all folder_id in FolderMove.folder_id_list
        if folder_move.folder_id_list:
            logging.info(f"[move_file_between_folders] Processing {len(folder_move.folder_id_list)} folders to move: {folder_move.folder_id_list}")
            
            for folder_id in folder_move.folder_id_list:    
                logging.info(f"[move_file_between_folders] Processing folder_id: {folder_id}")
                folder = await self.db.get(Folder, folder_id)
                
                if folder:
                    logging.info(f"[move_file_between_folders] Found folder: folder_id={folder.folder_id}, current_path={folder.folder_path}, current_depth={folder.depth_level}")
                    
                    # Get all descendant folders that need path updates
                    descendant_folders = await self.get_descendant_folder_ids_by_path(folder.folder_path)
                    logging.info(f"[move_file_between_folders] Found {len(descendant_folders)} descendant folders for folder_id={folder_id}")
                    
                    # Update each descendant folder's path and depth level
                    for descendant_folder in descendant_folders:
                        old_path = descendant_folder.folder_path
                        old_depth = descendant_folder.depth_level
                        
                        # Handle path update based on whether src_folder_path is empty
                        if src_folder_path!='':
                            if dest_folder_path!='':
                            # Normal case: replace the source path with destination path
                                if descendant_folder.folder_path.startswith(src_folder_path):
                                    descendant_folder.folder_path = descendant_folder.folder_path.replace(src_folder_path, dest_folder_path)
                                    logging.info(f"[move_file_between_folders] Path replacement: src='{src_folder_path}' -> dest='{dest_folder_path}'")
                            else:
                             # Extract the actual folder path starting from this folder_id and concatenate with dest_folder_path
                                logging.info(f"[move_file_between_folders] Processing case: src_folder_path='{src_folder_path}' is not empty, dest_folder_path='{dest_folder_path}' is empty")
                                logging.info(f"[move_file_between_folders] Extracting path from descendant_folder.folder_path='{descendant_folder.folder_path}' using folder.folder_id='{folder.folder_id}'")
                                
                                extracted_path = self.extract_folder_path_from_id(descendant_folder.folder_path, folder.folder_id)
                                logging.info(f"[move_file_between_folders] Extracted path: '{extracted_path}'")
                                
                                # Remove leading slash from extracted path and ensure dest_folder_path doesn't end with slash
                                clean_dest_path = dest_folder_path.rstrip('/')
                                clean_extracted_path = extracted_path.lstrip('/')
                                logging.info(f"[move_file_between_folders] Cleaned paths: clean_dest_path='{clean_dest_path}', clean_extracted_path='{clean_extracted_path}'")
                                
                                if clean_extracted_path:
                                    descendant_folder.folder_path = f"{clean_dest_path}/{clean_extracted_path}"
                                    logging.info(f"[move_file_between_folders] Concatenated path: '{descendant_folder.folder_path}'")
                                else:
                                    descendant_folder.folder_path = clean_dest_path
                                    logging.info(f"[move_file_between_folders] Using only dest_path: '{descendant_folder.folder_path}'")

                        else:
                            # Special case: src_folder_path is empty (moving from root)
                            logging.info(f"[move_file_between_folders] Processing case: src_folder_path='{src_folder_path}' is empty (moving from root)")
                            logging.info(f"[move_file_between_folders] Extracting path from descendant_folder.folder_path='{descendant_folder.folder_path}' using folder.folder_id='{folder.folder_id}'")
                            
                            extracted_path = self.extract_folder_path_from_id(descendant_folder.folder_path, folder.folder_id)
                            logging.info(f"[move_file_between_folders] Extracted path: '{extracted_path}'")
                            
                            # Remove leading slash from extracted path and ensure dest_folder_path doesn't end with slash
                            clean_dest_path = dest_folder_path.rstrip('/')
                            clean_extracted_path = extracted_path.lstrip('/')
                            logging.info(f"[move_file_between_folders] Cleaned paths: clean_dest_path='{clean_dest_path}', clean_extracted_path='{clean_extracted_path}'")
                            
                            if clean_extracted_path:
                                descendant_folder.folder_path = f"{clean_dest_path}/{clean_extracted_path}"
                                logging.info(f"[move_file_between_folders] Concatenated path: '{descendant_folder.folder_path}'")
                            else:
                                descendant_folder.folder_path = clean_dest_path
                                logging.info(f"[move_file_between_folders] Using only dest_path: '{descendant_folder.folder_path}'")
                            # descendant_folder.folder_path = f"{dest_folder_path}{descendant_folder.folder_id}/"
                                
                        
                        # Update the depth level by adding the difference between source and destination depths
                        descendant_folder.depth_level = descendant_folder.depth_level + depth_level_diference
                        
                        logging.info(f"[move_file_between_folders] Updating descendant folder: folder_id={descendant_folder.folder_id}, old_path='{old_path}' -> new_path='{descendant_folder.folder_path}', old_depth={old_depth} -> new_depth={descendant_folder.depth_level}")
                        
                        # Commit the changes to the database
                        await self.db.commit()
                        logging.info(f"[move_file_between_folders] Committed changes for descendant folder: folder_id={descendant_folder.folder_id}")
                        
                        # Refresh the object from database to ensure we have the latest data
                        await self.db.refresh(descendant_folder)
                        logging.info(f"[move_file_between_folders] Refreshed descendant folder from database: folder_id={descendant_folder.folder_id}, verified_path='{descendant_folder.folder_path}', verified_depth={descendant_folder.depth_level}")

                    logging.info(f"[move_file_between_folders] Completed processing folder_id={folder_id} with {len(descendant_folders)} descendants updated")
                else:
                    logging.warning(f"[move_file_between_folders] Folder not found for folder_id: {folder_id}")

            await self.db.execute(
                update(Folder)
                .where(Folder.folder_id.in_(folder_move.folder_id_list))
                .values(parent_folder_id=folder_move.dest_folder_id)
            )
        else:
            logging.info("[move_file_between_folders] No folder_id_list provided.")

        await self.db.commit()
        logging.info(f"[move_file_between_folders] Completed. Returning folder_path={folder_path}, depth_level={depth_level}")

        # Update modified_at for all parent folders
        if folder_path:
            await self.update_parent_folders_modified_at(folder_path)

        return {
            "folder_path": folder_path,
            "depth_level": depth_level
        }


    async def update_folder_path_depth_level(self, folder_id: str, depth_level: int, folder_path: str):
        folder = await self.get_folder_by_id(folder_id)
        if folder:
            folder.folder_path = folder.folder_path.replace(folder.folder_id, folder_id)
            folder.depth_level = folder.depth_level + 1
            await self.db.commit()
            await self.db.refresh(folder)
            return folder


    async def merge_folders(self, folder_id: str, folder_id_to_merge: str):
        logging.info(f"[merge_folders] Called with folder_id={folder_id}, folder_id_to_merge={folder_id_to_merge}")


        folder_data = await self.get_folder_by_id(folder_id)
        # Update user_reports to point to the new folder
        await self.db.execute(
            update(UserReports)
            .where(UserReports.folder_id == folder_id_to_merge)
            .where(UserReports.user_id == self.user_id)
            .values(folder_id=folder_id)
        )
        logging.info(f"[merge_folders] UserReports updated from folder_id={folder_id_to_merge} to folder_id={folder_id}")


        # Update child folders to point to the new parent
        await self.db.execute(
            update(Folder)
            .where(Folder.parent_folder_id == folder_id_to_merge)
            .where(Folder.user_folder_id == self.user_id)
            .values(parent_folder_id=folder_id)
        )
        logging.info(f"[merge_folders] Child folders updated to new parent_folder_id={folder_id}")

        await self.db.commit()
        logging.info(f"[merge_folders] Commit after user_reports and child folders update.")


        # Update modified_at for the merged-into folder
        await self.db.execute(
            update(Folder)
            .where(Folder.folder_id == folder_id)
            .where(Folder.user_folder_id == self.user_id)
            .values(modified_at=datetime.now(timezone.utc))
        )
        logging.info(f"[merge_folders] Updated modified_at for folder_id={folder_id}")

        # Update modified_at for all parent folders
        if folder_data.folder_path:
            await self.update_parent_folders_modified_at(folder_data.folder_path)

        # Delete the merged folder

        await self.delete_folder(folder_id=folder_id_to_merge)

        logging.info(f"[merge_folders] Deleted folder_id={folder_id_to_merge}.")

        await self.db.commit()
        logging.info(f"[merge_folders] Final commit after all updates.")

        return folder_id


    async def delete_folder(self, folder_id: str):
        logging.info(f"[delete_folder] Called with folder_id={folder_id}")
        # Load the folder by ID
        folder_data = await self.get_folder_by_id(folder_id)

        if not folder_data:
            logging.warning(f"[delete_folder] Folder not found: {folder_id}")
            raise NotFoundError(resource=f"Pasta {folder_id}")
        logging.info(f"[delete_folder] Deleting folder and its subfolders: {folder_id}")


        folder_descendet = await self.get_folders_by_parent_id(parent_folder_id=folder_id)
        logging.info(f"[delete_folder] Found {len(folder_descendet)} descendant folders")
        if folder_descendet:
            for folder_desc in folder_descendet:
                #get all user_reports in the folder
                await self.delete_folder(folder_desc.folder_id)
                
                await self.delete_user_reports_in_folder(folder_desc.folder_id)

        logging.info(f"[delete_folder] Deleted all descendant folders")

        await self.delete_user_reports_in_folder(folder_id)


        await self.delete_hmacs_folder(folder_id)
        logging.info(f"[delete_folder] HMACs deleted for folder_id: {folder_id}")
        await self.db.execute(
            delete(Folder)
            .where(Folder.folder_id == folder_id)
            .where(Folder.user_folder_id == self.user_id)
        )
        await self.db.commit()

        # Update modified_at for all parent folders
        if folder_data.folder_path:
            await self.update_parent_folders_modified_at(folder_data.folder_path)

        logging.info(f"[delete_folder] Deleted folder and all related subfolders and user_reports: {folder_id}")


    async def delete_user_reports_in_folder(self, folder_id: str):
        from services.report_service import UserReportsService
        user_reports = await self.db.execute(
            select(UserReports)
            .where(UserReports.folder_id == folder_id).where(UserReports.user_id==self.user_id)
        )
        user_reports = user_reports.scalars().all()

        for user_report in user_reports:
            report_service = UserReportsService(db=self.db, user_id=self.user_id, user_reports_id = user_report.user_reports_id)
            await report_service.delete_report_handler()
            logging.info(f"[delete_user_reports_in_folder] Deleted {len(user_reports)} user_reports in folder: {folder_id}")


    async def get_folders_except(self, folder_id: str, folder_ids_hmac: list[str] = None, 
                                 page: int = 1, limit: int = DefaultPageLogs.pagedefault, order: str = "desc", 
                                 column_order: str = "modified_at"):
        """Get all folders except the one and children"""
        logging.info(f"[get_folders_except] Called with folder_id={folder_id}, folder_ids_hmac={folder_ids_hmac}, page={page}, limit={limit}, order={order}, column_order={column_order}")

        base_query = (select(Folder.folder_id,
                       Folder.folder_name,
                       Folder.created_at,
                       Folder.modified_at)
            .where(Folder.user_folder_id == self.user_id))
        logging.info(f"[get_folders_except] Created base query with user filter: user_id={self.user_id}")
        
        count_query = select(func.count(Folder.folder_id)).where(Folder.user_folder_id == self.user_id)
        logging.info(f"[get_folders_except] Created count query with user filter: user_id={self.user_id}")

        common_filters = []
        logging.info(f"[get_folders_except] Initialized common_filters list")

        if folder_id:
            logging.info(f"[get_folders_except] Processing folder_id filter: {folder_id}")
            common_filters.append(~Folder.folder_path.contains(folder_id))
            logging.info(f"[get_folders_except] Added filter: ~Folder.folder_path.contains({folder_id})")
            folder_data = await self.db.get(Folder, folder_id)
            if folder_data:
                if folder_data.parent_folder_id:
                    common_filters.append(Folder.folder_id != folder_data.parent_folder_id)
                    logging.info(f"[get_folders_except] Added filter: Folder.folder_id != {folder_id}")
                else:
                    logging.info(f"[get_folders_except] Folder has no parent_folder_id, skipping parent filter")

        else:
            logging.info(f"[get_folders_except] No folder_id provided, skipping folder-specific filters")
    
        if folder_ids_hmac:
            logging.info(f"[get_folders_except] Processing HMAC filter: {folder_ids_hmac}")
            common_filters.append(Folder.folder_id.in_(folder_ids_hmac))
            logging.info(f"[get_folders_except] Added filter: Folder.folder_id.in_({len(folder_ids_hmac)} items)")
        else:
            logging.info(f"[get_folders_except] No folder_ids_hmac provided, skipping HMAC filter")

        organization_users_service = OrganizationUsersService(db=self.db, user_id=self.user_id)
        org_data = await organization_users_service.get_organization_user()
        if org_data:
            logging.info(f"[get_folders_except] User belongs to organization: organization_id={org_data.organization_id}")
            common_filters.append(Folder.organization_id == org_data.organization_id)
            logging.info(f"[get_folders_except] Added organization filter: organization_id={org_data.organization_id}")
        else:
            logging.info(f"[get_folders_except] User does not belong to any organization, skipping organization filter")

        logging.info(f"[get_folders_except] Total common_filters count: {len(common_filters)}")
        if common_filters:
            logging.info(f"[get_folders_except] Applying common filters to both base and count queries")
            base_query = base_query.where(and_(*common_filters))
            count_query = count_query.where(and_(*common_filters))
        else:
            logging.info(f"[get_folders_except] No common filters to apply")

        page = max(1, page)
        offset = (page - 1) * limit if limit else None
        logging.info(f"[get_folders_except] Pagination: page={page}, limit={limit}, offset={offset}")

        column_map = {
            "created_at": Folder.created_at,
            "modified_at": Folder.modified_at,
        }
        logging.info(f"[get_folders_except] Column mapping: {list(column_map.keys())}")

        sort_column = column_map.get(column_order, Folder.modified_at)
        sort_func = asc if order.lower() == "asc" else desc
        logging.info(f"[get_folders_except] Sorting: column={column_order}, function={sort_func.__name__}")

        base_query = base_query.order_by(sort_func(sort_column))
        logging.info(f"[get_folders_except] Applied ordering to base query")

        if limit:
            base_query = base_query.limit(limit)
            logging.info(f"[get_folders_except] Applied limit: {limit}")
            if offset:
                base_query = base_query.offset(offset)
                logging.info(f"[get_folders_except] Applied offset: {offset}")

        try:
            logging.info(f"[get_folders_except] Executing base query to fetch data")
            # Execute both queries
            result = await self.db.execute(base_query)
            rows = result.mappings().all()
            logging.info(f"[get_folders_except] Base query returned {len(rows)} rows")

            logging.info(f"[get_folders_except] Executing count query to get total items")
            count_result = await self.db.execute(count_query)
            total_items = count_result.scalar()
            logging.info(f"[get_folders_except] Count query returned total_items: {total_items}")

            # Calculate pagination metadata
            total_pages = math.ceil(total_items / limit) if limit > 0 else 1
            has_next = page < total_pages
            has_previous = page > 1
            logging.info(f"[get_folders_except] Pagination metadata: total_pages={total_pages}, has_next={has_next}, has_previous={has_previous}")

            data = [dict(r) for r in rows]
            logging.info(f"[get_folders_except] Converted {len(data)} rows to dictionaries")

            pagination_metadata = PaginationMetadata(
                current_page=page,
                total_pages=total_pages,
                total_items=total_items,
                page_size=limit,
                has_next=has_next,
                has_previous=has_previous
            )
            logging.info(f"[get_folders_except] Created pagination metadata object")

            logging.info(f"[get_folders_except] Returning PaginatedResponse with {len(data)} items")
            return PaginatedResponse(data=data, pagination=pagination_metadata)
        except Exception as e:
            logging.error(f"[get_folders_except] Error executing query for user {self.user_id}: {e}")
            raise


    async def get_folders_except_actual(self, folder_id: str, folder_ids_hmac: list[str] = None, 
                                page: int = 1, limit: int = DefaultPageLogs.pagedefault, order: str = "desc", 
                                column_order: str = "modified_at"):
        """Get all folders except the one and children"""

        base_query = (select(Folder.folder_id,
                       Folder.folder_name,
                       Folder.created_at,
                       Folder.modified_at)
            .where(Folder.user_folder_id == self.user_id))
        
        count_query = select(func.count(Folder.folder_id)).where(Folder.user_folder_id == self.user_id)

        if folder_id:
            base_query = base_query.where(Folder.folder_id != folder_id)
            count_query = count_query.where(Folder.folder_id != folder_id)

        common_filters = []
    
        if folder_ids_hmac:
            common_filters.append(Folder.folder_id.in_(folder_ids_hmac))

        organization_users_service = OrganizationUsersService(db=self.db, user_id=self.user_id)
        org_data = await organization_users_service.get_organization_user()
        if org_data:
            common_filters.append(Folder.organization_id == org_data.organization_id)

        if common_filters:
            base_query = base_query.where(and_(*common_filters))
            count_query = count_query.where(and_(*common_filters))

        page = max(1, page)
        offset = (page - 1) * limit if limit else None


        column_map = {
            "created_at": Folder.created_at,
            "modified_at": Folder.modified_at,
        }

        sort_column = column_map.get(column_order, Folder.modified_at)
        sort_func = asc if order.lower() == "asc" else desc

        base_query = base_query.order_by(sort_func(sort_column))


        if limit:
            base_query = base_query.limit(limit)
            if offset:
                base_query = base_query.offset(offset)

        try:
            # Execute both queries
            result = await self.db.execute(base_query)
            rows = result.mappings().all()

            count_result = await self.db.execute(count_query)
            total_items = count_result.scalar()

            # Calculate pagination metadata
            total_pages = math.ceil(total_items / limit) if limit > 0 else 1
            has_next = page < total_pages
            has_previous = page > 1

            data = [dict(r) for r in rows]

            pagination_metadata = PaginationMetadata(
                current_page=page,
                total_pages=total_pages,
                total_items=total_items,
                page_size=limit,
                has_next=has_next,
                has_previous=has_previous
            )

            return PaginatedResponse(data=data, pagination=pagination_metadata)
        except Exception as e:
            logger.error("[get_reports_by_folder][user(%s)] Error executing query: %s", self.user_id, e)
            raise


    async def delete_hmacs_folder(self, folder_id: str):
        """Delete HMACs for a folder"""
        if not folder_id:
            logger.error("[delete_hmacs_folder] No folder_id provided")
            raise ValueError("folder_id is required")

        try:
            logger.info("[delete_hmacs_folder] Deleting hmacs for folder_id: %s", folder_id)


            stmt = delete(FolderHmac).where(FolderHmac.folder_id == folder_id)
            await self.db.execute(stmt)
            await self.db.commit()

            logger.info("[delete_hmacs_folder] Successfully deleted hmacs for folder_id: %s", folder_id)

        except Exception as e:
            logger.error("[delete_hmacs_folder] Error deleting hmacs for folder_id %s: %s", folder_id, e)
            await self.db.rollback()
            raise


    async def get_folder_by_id(self, folder_id: str) -> Folder:
        logging.info(f"[get_folder_by_id] Called with folder_id={folder_id}")
        folder = await self.db.get(Folder, folder_id)
        if not folder:
            logging.warning(f"[get_folder_by_id] Folder not found: {folder_id}")
            raise NotFoundError(resource=f"Pasta {folder_id}")
        logging.info(f"[get_folder_by_id] Found folder: {folder}")
        return folder


    async def get_folders_by_parent_id(self, parent_folder_id: str) -> list[Folder]:
        logging.info(f"[get_folders_by_parent_id] Called with parent_folder_id={parent_folder_id}")

        result = await self.db.execute(select(Folder).where(Folder.parent_folder_id == parent_folder_id))
        folders = result.scalars().all()
        logging.info(f"[get_folders_by_parent_id] Found {len(folders)} folders with parent_folder_id={parent_folder_id}")
        return folders


    async def get_descendant_folder_ids_by_path(self, folder_path: str = None) -> list[Folder]:

        filters = [Folder.user_folder_id == self.user_id]

        if folder_path:
            filters.append(Folder.folder_path.contains(folder_path))

        query = select(Folder).where(*filters)
        result = await self.db.execute(query)
        folders = result.scalars().all()
        return folders


    async def get_descendant_folders_excluding_self(self, folder_path: str, exclude_folder_id: str) -> list[Folder]:
        """
        Get all descendant folders whose folder_path contains the target folder_path,
        excluding the target folder itself.

        Args:
            folder_path: The folder path to search for descendants
            exclude_folder_id: The folder_id to exclude from results

        Returns:
            List of Folder objects that are descendants but not the target folder
        """


        filters = [
            Folder.user_folder_id == self.user_id,
            Folder.folder_path.contains(folder_path),
            Folder.folder_id != exclude_folder_id
        ]

        query = select(Folder).where(and_(*filters)).order_by(Folder.modified_at.desc())
        result = await self.db.execute(query)
        folders = result.scalars().all()

        logging.info(f"[get_descendant_folders_excluding_self] Found {len(folders)} descendant folders (excluding {exclude_folder_id})")
        return folders


    async def list_user_folders(self, limit: int = 25, page: int = 1, 
                                order: str = "modified_at", column_order: str = "desc", 
                                hmac_filter: List = None, folder_id: str = None) -> List[Folder]:
        """List user folders"""
        logger.info(f"[list_user_folders] Called with limit={limit}, page={page}, order={order}, column_order={column_order}, hmac_filter={hmac_filter}, folder_id={folder_id}")

        if hmac_filter:
            logger.info(f"[list_user_folders] Filtering using HMAC: {hmac_filter}")
            folder_ids = await self.filter_using_hmac_folder(hmac_filter)
            logger.info(f"[list_user_folders] Found {len(folder_ids)} folder_ids matching HMAC filter")
            if not folder_ids:
                logger.info(f"[list_user_folders] No folder_ids found for HMAC filter {hmac_filter}, returning empty list")
                return []

            query = select(Folder).where(Folder.folder_id.in_(folder_ids)).where(Folder.user_folder_id == self.user_id)
            # if folder_id:
            logger.info(f"[list_user_folders] Filtering by parent_folder_id: {folder_id}")
            query = query.where(Folder.parent_folder_id == folder_id)
        else:
            logger.info(f"[list_user_folders] No HMAC filter provided, filtering by user_folder_id: {self.user_id}")
            query = select(Folder).where(Folder.user_folder_id == self.user_id)
            # if folder_id:
            logger.info(f"[list_user_folders] Filtering by parent_folder_id: {folder_id}")
            query = query.where(Folder.parent_folder_id == folder_id)

        logger.info(f"[list_user_folders] Applying contextual filters to query")
        query = await self._apply_contextual_filters_to_query(query, is_user_scope=True, model_class=Folder)
        logger.info(f"[list_user_folders] Ordering query by {column_order} {order}")
        
        organization_users_service = OrganizationUsersService(db=self.db, user_id=self.user_id)
        org_data = await organization_users_service.get_organization_user()
        if org_data:
            logger.info(f"[list_user_folders] Active organization found for user: {self.user_id}")
            query = query.where(Folder.organization_id == org_data.organization_id)

        # Map column names to actual SQLAlchemy column attributes
        column_map = {
            "created_at": Folder.created_at,
            "modified_at": Folder.modified_at,
            "folder_name": Folder.folder_name,
            "depth_level": Folder.depth_level
        }
        
        if column_order not in column_map:
            logger.warning(f"[list_user_folders] Invalid column_order: '{column_order}', defaulting to 'modified_at'")
            column_order = "modified_at"
        
        sort_column = column_map[column_order]
        sort_func = asc if order.lower() == "asc" else desc
        query = query.order_by(sort_func(sort_column))
        logger.info(f"[list_user_folders] Applying offset and limit: offset={(page - 1) * limit}, limit={limit}")
        query = query.limit(limit*page)
        logger.info(f"[list_user_folders] Executing query")
        result = await self.db.execute(query)
        folders = result.scalars().all()
        logger.info(f"[list_user_folders] Query returned {len(folders)} folders")

        # Convert each Folder object to a dictionary
        folder_dicts = []
        for folder in folders:
            logger.info(f"[list_user_folders] Adding folder to result: folder_id={folder.folder_id}, folder_name={folder.folder_name}")
            folder_dicts.append({
                "folder_id": str(folder.folder_id),
                "folder_name": folder.folder_name,
                "parent_folder_id": str(folder.parent_folder_id) if folder.parent_folder_id else None,
                "folder_path": folder.folder_path,
                "depth_level": folder.depth_level,
                "created_at": folder.created_at,
                "modified_at": folder.modified_at
            })

        logger.info(f"[list_user_folders] Returning {len(folder_dicts)} folder dicts")
        return folder_dicts


    async def filter_using_hmac_folder(self, hmac_filter: List[str]) -> List[str]:
        """Filter folders using HMAC - must match ALL hmacs in hmac_filter."""
        logger.info(f"[filter_using_hmac_folder] Called with hmac_filter={hmac_filter}")
        folder_ids = []

        if hmac_filter:
            filters = [FolderHmac.hmac.in_(hmac_filter)]
            try:
                logger.info(f"[filter_using_hmac_folder] Building select statement for folder_ids matching ALL hmacs")
                stmt = (
                    select(FolderHmac.folder_id)
                    .where(and_(*filters))
                    .group_by(FolderHmac.folder_id)
                    .having(func.count(distinct(FolderHmac.hmac)) == len(hmac_filter))
                )
                logger.info(f"[filter_using_hmac_folder] Executing statement")
                result = await self.db.execute(stmt)
                folder_ids = [row[0] for row in result.fetchall()]
                logger.info(f"[filter_using_hmac_folder] Fetched folder_ids using hmac filter: {hmac_filter} => {folder_ids}")

                if not folder_ids:
                    logger.warning(f"[filter_using_hmac_folder] No folder_id found with given filters: hmac={hmac_filter}")
                    return []

            except Exception as e:
                logger.error(f"[filter_using_hmac_folder] Error executing HMAC filter query: {e}")
                raise

        logger.info(f"[filter_using_hmac_folder] Returning {len(folder_ids)} folder_ids")
        return folder_ids


    async def collect_folder_path(self, folder_id: str) -> list[Folder]:
        """
        Recursively collect all parent folders from a given folder_id up to the root.
        Returns the path from root to leaf.
        """
        logging.info(f"[collect_folder_path] Collecting path for folder_id: {folder_id}")
        path = []
        current_folder_id = folder_id

        while current_folder_id:
            folder = await self.get_folder_by_id(current_folder_id)
            if not folder:
                logging.warning(f"[collect_folder_path] Folder not found: {current_folder_id}")
                break
            path.append(folder)
            current_folder_id = folder.parent_folder_id

        # Reverse to get root to leaf order
        path = path[::-1]
        logging.info(f"[collect_folder_path] Collected path with {len(path)} folders")
        return path


    def extract_folder_path_from_id(self, full_path: str, folder_id: str) -> str:
        """
        Extract the folder path starting from a specific folder_id.
        
        Args:
            full_path (str): The complete folder path
            folder_id (str): The folder ID to start the path from
            
        Returns:
            str: The folder path starting from the specified folder_id, or the original path if folder_id not found
        """
        logging.info(f"[extract_folder_path_from_id] Extracting path from folder_id={folder_id} in full_path='{full_path}'")
        
        if not full_path or not folder_id:
            logging.warning(f"[extract_folder_path_from_id] Empty full_path or folder_id: full_path='{full_path}', folder_id='{folder_id}'")
            return full_path
        
        # Find the position of the folder_id in the path
        folder_id_with_slashes = f"/{folder_id}/"
        start_pos = full_path.find(folder_id_with_slashes)
        
        if start_pos != -1:
            # Extract the path starting from the folder_id
            extracted_path = full_path[start_pos:]
            logging.info(f"[extract_folder_path_from_id] Extracted path: '{extracted_path}' from position {start_pos}")
            return extracted_path
        else:
            # If folder_id not found, return the original path
            logging.warning(f"[extract_folder_path_from_id] Folder ID '{folder_id}' not found in path '{full_path}'")
            return full_path

    async def get_next_uuid_after_path(self, base_folder_path: str, target_folder_path: str) -> str:
        """
        Extract the next UUID after the base folder path from a target folder path.

        Args:
            base_folder_path: The base path to search for (e.g., '/123/456/')
            target_folder_path: The target path to extract from (e.g., '/123/456/4667/234/')

        Returns:
            The next UUID after the base path, or None if not found
        """


        # Ensure both paths end with '/'
        if not base_folder_path.endswith('/'):
            base_folder_path += '/'
        if not target_folder_path.endswith('/'):
            target_folder_path += '/'

        # Check if target path starts with base path
        if not target_folder_path.startswith(base_folder_path):
            return None

        # Remove the base path from the target path
        remaining_path = target_folder_path[len(base_folder_path):]

        # Extract the first UUID from the remaining path
        # UUID pattern: 8-4-4-4-12 hexadecimal characters
        uuid_pattern = r'^([0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12})'
        match = re.match(uuid_pattern, remaining_path)

        if match:
            return match.group(1)

        return None