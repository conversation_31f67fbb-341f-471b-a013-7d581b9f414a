import logo from "~/assets/logo.svg";
import icon from "~/assets/reports_logo.svg";
import { ChamferBox, Text } from "@snap/design-system";
import UserNavbar from "~/components/UserNavbar";
import { useFolderNavigation } from "~/hooks/useFolderNavigation";
import { Link } from "react-router";

const Header = () => {
  const { navigateToRoot } = useFolderNavigation();
  return (
    <header className="flex p-8 sticky gap-4 md:gap-8 z-50">
      <div className=" w-full flex items-center gap-4 md:gap-8">
        <ChamferBox
          corner="topRight"
          className="h-fit max-w-none min-w-fit min-h-full"
        >
          <Link to={"/"} onClick={(e) => { e.preventDefault(); navigateToRoot(); }}>
            <div className="flex items-start gap-4">

              <img
                width={200}
                src={logo}
                alt="SNAP Reports logo"
                className="hidden sm:block"
              />
              <img src={icon} alt="SNAP Reports icon" />
              <Text variant="label-lg" className="leading-none hidden sm:block">REPORTS</Text>
            </div>
          </Link>
        </ChamferBox>
        <div>
          <UserNavbar />
        </div>
      </div>
      {/* <ModeToggle /> */}
    </header>
  );
};

export default Header;
