import { Button, Input, Text } from "@snap/design-system";
import { Check } from "lucide-react";
import { useState } from "react";
import { AiOutlineLoading3Quarters } from "react-icons/ai";
import { useReportCRUD } from "~/hooks/useReportCRUD";
import { useParams } from "react-router";

export function RenameReportDialog({
  reportName,
  onCancel,
  onConfirm,
}: {
  reportName: string;
  onCancel: () => void;
  onConfirm: (newName: string) => void;
}) {
  const { folderId } = useParams<{ folderId?: string }>();
  const [name, setName] = useState(reportName);
  const { renameReportMutation } = useReportCRUD(folderId || null);

  return (
    <>
      <Text className="block mb-1">Digite o novo nome do relatório:</Text>
      <Input
        type="text"
        variant="outlined"
        value={name}
        onChange={e => setName(e.target.value)}
        className="rounded-none border-0 w-full border-b dashed pl-0"
        maxLength={100}
        autoFocus
      />
      <div className="flex gap-3 mt-8">
        <Button onClick={onCancel} className="uppercase !bg-transparent">
          Cancelar
        </Button>
        <Button
          onClick={() => onConfirm(name.trim())}
          className="uppercase !bg-foreground !text-background !font-bold disabled:opacity-50"
          icon={renameReportMutation.isPending ? <AiOutlineLoading3Quarters size={16} /> : <Check size={16} />}
          iconPosition="right"
          disabled={name.trim() === reportName || renameReportMutation.isPending}
        >
          CONFIRMAR
        </Button>
      </div>
    </>
  );
}
