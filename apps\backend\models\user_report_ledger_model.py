from sqlalchemy import Column, ForeignKey, DateTime, UUID, Text
from sqlalchemy.sql import func, text
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import UUID as PostgresUUID

from models.base import Base

class UserReportLedger(Base):
    __tablename__ = 'user_report_ledger'
    __table_args__ = {"schema": "public"}

    user_reports_id = Column(PostgresUUID, primary_key=True, server_default=func.gen_random_uuid())
    created_at = Column(DateTime(timezone=True), nullable=False, server_default=func.now(), index=True)
    status_final = Column(Text, nullable=False, index=True, server_default=text("'pendente'"))
    updated_at = Column(DateTime(timezone=True), nullable=False, server_default=func.now(), index=True)
    organization_id = Column(PostgresUUID, ForeignKey("public.organizations.organization_id"), nullable=True, index=True)
    report_type = Column(Text, nullable=False, index=True)
    user_id = Column(PostgresUUID, ForeignKey("public.users.user_id"), nullable=False, index=True)

    # Relationships
    user = relationship("Users", back_populates="user_report_ledger")
    user_report = relationship("UserReports", back_populates="user_report_ledger", foreign_keys="UserReports.user_reports_id")
    report_executions = relationship("ReportExecutions", back_populates="user_report_ledger")

    def __repr__(self):
        return f"<UserReportLedger(user_reports_id={self.user_reports_id}, status_final={self.status_final}, report_type={self.report_type})>"

