import { FolderModel } from "root/domain/entities/folder.model";
import { getInitials } from "~/helpers";
import {
  SubjectCard,
  Separator,
  Avatar,
  Button,
  Text,
  Loading,
} from "@snap/design-system";
import { <PERSON>R<PERSON>, Pencil, Trash, X } from "lucide-react"
import { MdFolderCopy } from "react-icons/md";
import { useNavigate } from "react-router";
import { useReportListActions } from "~/store/reportListStore";
import { useBreadcrumbsActions } from "~/store/breadcrumbsStore";
import { REPORT_CONSTANTS } from "~/helpers/constants";
import { useFolderCRUD } from "~/hooks/useFolderCRUD";
import { useDialogActions } from "~/store/dialogStore";
import { RenameFolderDialog } from "./RenameFolderDialog";
import { MoveFolderDialog } from "./MoveFolderDialog";
import { MdOutlineDriveFileMove } from "react-icons/md";

export default function FolderCard({
  folder,
  onAccess,
}: {
  folder: FolderModel;
  onAccess?: () => void;
}) {
  const { deleteFolderMutation, renameFolderMutation } = useFolderCRUD();
  const { openDialog, closeDialog } = useDialogActions();
  const navigate = useNavigate();
  const { resetPagination, addFolderToPath } = useReportListActions();
  const { addItem, setDirectNavigationMode } = useBreadcrumbsActions();

  const childFolders = folder.childFolders;
  const childReports = folder.childReports;

  const handleOpenFolder = () => {
    resetPagination();

    addFolderToPath({
      id: folder.id,
      name: folder.name
    });

    addItem({
      title: folder.name,
      onClick: () => navigate(`/pasta/${folder.id}`),
    });

    setDirectNavigationMode(false);

    navigate(`/pasta/${folder.id}`);
    if (onAccess) onAccess();
  };

  const handleConfirmDelete = () => {
    openDialog({
      title: "Excluir pasta",
      icon: <Trash />,
      content: (
        <div>
          <Text variant="body-md">
            Tem certeza que deseja excluir a pasta <span className="font-bold text-accent uppercase">{folder.name}</span>?
          </Text>
          <Text variant="body-md" className="mt-2">
            Todos os <span className="font-bold">relatórios</span> e <span className="font-bold">pastas</span> dentro dela serão excluídos.
          </Text>
        </div>
      ),
      footer: (
        <div className="flex gap-3">
          <Button
            className="uppercase !bg-transparent"
            onClick={() => {
              closeDialog();
            }}
          >
            Cancelar
          </Button>
          <Button
            onClick={() => {
              deleteFolderMutation.mutate(folder.id);

            }}
            className="uppercase !bg-foreground !text-background !font-bold"
            icon={<Trash size={16} />}
            iconPosition="right"
          >
            Excluir
          </Button>
        </div>
      ),
    });
  };

  const handleRenameFolder = () => {
    openDialog({
      title: "Renomear pasta",
      icon: <Pencil />,
      content: (
        <RenameFolderDialog
          folderName={folder.name}
          onCancel={() => closeDialog()}
          onConfirm={(newName) => {
            if (newName && newName !== folder.name) {
              renameFolderMutation.mutate({
                [REPORT_CONSTANTS.new_folder.folder_id]: folder.id,
                [REPORT_CONSTANTS.new_folder.folder_name]: newName,
              });
            }
          }}
        />
      )
    });
  };

  const handleMoveFolder = () => {
    openDialog({
      title: "Mover pasta para outra pasta",
      icon: <MdOutlineDriveFileMove size={24} />,
      content: <MoveFolderDialog.Content currentFolderId={folder.id} />,
      footer: (
        <MoveFolderDialog.Footer
          folderId={folder.id}
          currentFolderId={folder.parentId || undefined}
          onMoveSuccess={() => {
            console.log("Folder moved successfully");
          }}
        />
      ),
      className: "max-w-4xl",
    });
  };

  const header = (
    <>
      <div
        className="flex items-center bg-background justify-between gap-3 p-3 w-full cursor-pointer hover:bg-muted/50 transition-colors duration-200 group"
        onClick={handleOpenFolder}
      >
        <MdFolderCopy className="size-8 text-secondary" />
        <span className="uppercase line-clamp-2 text-ellipsis max-w-4/5 group-hover:underline">
          {folder.name}
        </span>
      </div>
      <Separator />
    </>
  );

  const content = (
    <div className="flex flex-wrap gap-3.5 p-3 justify-start" id="this is crazy">
      {childFolders.map((childFolder) => (
        <div key={childFolder.folder_id} className="border-border border-4 rounded-full p-3" title={childFolder.folder_name} >
          <MdFolderCopy className="size-5 text-secondary" />
        </div>
      ))}

      {childReports.map((report) => {
        const status = typeof report.report_status === 'object' ? report.report_status.status_report : report.report_status;
        const isPending = status === REPORT_CONSTANTS.status.pending;
        const isError = status === REPORT_CONSTANTS.status.error;

        return (
          <div key={report.user_reports_id} title={report.subject_name || status}>
            {isPending ? (
              <div className="border-border border-4 rounded-full p-3" title={report.subject_name}>
                <Loading size="lg" color="var(--accent)" />
              </div>
            ) : isError ? (
              <div className="border-border border-4 rounded-full p-3" title={report.subject_name}>
                <X className="size-5 text-destructive" />
              </div>
            ) : (
              <Avatar
                fallback={getInitials(report.subject_name) || "N/A"}
                size="md"
                textAlign="right"
                textClassName="text-sm uppercase"
              />
            )}
          </div>
        );
      })}

      {(childFolders.length === 0 && childReports.length === 0) && (
        <Text variant="body-md" align="center" className="py-2">
          Pasta vazia
        </Text>
      )}
      {
        (folder.numberOfItems && folder.numberOfItems > 0) && (
          <div className="flex items-center justify-center rounded-full p-2 bg-border h-[52px] w-[52px] overflow-hidden" title={`+${folder.numberOfItems} itens`}>
            <Text variant="body-md" align="center" className="font-bold truncate w-full text-center">
              +{folder.numberOfItems}
            </Text>
          </div>
        )
      }
    </div>
  );

  const actions = (
    <Button
      size="sm"
      variant="outline"
      iconPosition="right"
      icon={<ArrowRight className="size-4" />}
      className="w-full"
      onClick={handleOpenFolder}
    >
      Abrir
    </Button>
  );

  const menu = [
    {
      label: "mover para pasta",
      icon: <MdOutlineDriveFileMove className="size-4" />,
      onClick: handleMoveFolder,
    },
    // {
    //   label: "mesclar pastas",
    //   icon: <BiMerge className="size-4" />,
    //   onClick: () => alert("AÇÃO DE RENOMEAR PASTA ESTÁ EM CONSTRUÇÃO"),
    // },
    {
      label: "renomear pasta",
      icon: <Pencil className="size-4" />,
      onClick: handleRenameFolder,
    },
    {
      label: "Excluir pasta",
      icon: <Trash className="size-4" />,
      onClick: handleConfirmDelete,
    },
  ];

  return (
    <SubjectCard
      headerClassName="p-0"
      header={header}
      content={content}
      actions={actions}
      menu={menu}
      menuWrapperClassName="top-0 right-0"
      markShadowClassName="!bg-transparent"
      contentClassName="!bg-background [&>div>div]:!pb-0 [&>div>div]:!space-y-0 !mt-0 !px-0"
      menuClassName="border border-card"
      cardClassName="max-w-[282px]"
      footerClassName="[&_p]:text-sm !bg-background"
      menuTriggerType="click"
      border={true}
      footer={{
        createdAt: folder.createdAt,
        updatedAt: folder.modifiedAt,
      }}
    />
  );
}