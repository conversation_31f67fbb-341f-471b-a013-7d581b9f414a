import asyncio
import logging
import os

from fastapi import (APIRouter, Request, Response, HTTPException, Depends)

from kafka_consumer import send_pdf_request_and_wait

from services.minio_service import load_generated_pdf, delete_generated_pdf, delete_temp_pdf_data
from services.auth_service import auth_guard

from utils.jwt_utils import JWTUtils

from core.constants import Roles

from exceptions.business_exceptions import InvitePermissionDeniedError

logger = logging.getLogger(__name__)

DELETE_PDFS_AFTER_DOWNLOAD = os.getenv('DELETE_PDFS_AFTER_DOWNLOAD', 'true').lower() == 'true'

router = APIRouter()

@router.post("/create/pdf")
async def create_pdf(request: Request,
                     user=Depends(auth_guard)):
    
    token_decoded = JWTUtils(user_jwt=user)
    user_id = token_decoded.get_user_id()
    user_roles = token_decoded.get_user_roles()

    if Roles.create_pdf not in user_roles:
        logger.warning("[create_pdf] Access denied - User %s does not have create pdf role", user_id)
        raise InvitePermissionDeniedError(action="create pdf")

    try:
        data = await request.json()

        # Extract metadata from the data
        metadata = data.get('metadata', {})
        user_reports_id = metadata.get('user_reports_id')
        report_type = metadata.get('report_type')
        logger.info("[create_pdf] User reports id: %s", user_reports_id)
        logger.info("[create_pdf] Report type: %s", report_type)

        # Add user context to data for MinIO storage
        data['user_context'] = {
            'user_id': user_id,
            'user_reports_id': user_reports_id,
            'report_type': report_type
        }

        # Send request and get PDF reference with retry logic
        max_retries = 5
        retry_delay = 60  # seconds
        result = None
        pdf_reference = None
        filename = None
        
        for attempt in range(max_retries):
            try:
                logger.info("[create_pdf] Attempting PDF generation - attempt %d/%d", attempt + 1, max_retries)
                result = await send_pdf_request_and_wait(data)
                
                pdf_reference = result.get("pdfReference")
                filename = result.get("filename", "report.pdf")
                
                if pdf_reference:
                    logger.info("[create_pdf] PDF reference obtained successfully on attempt %d", attempt + 1)
                    break
                else:
                    logger.warning("[create_pdf] No PDF reference received on attempt %d/%d", attempt + 1, max_retries)
                    
            except Exception as e:
                logger.warning("[create_pdf] PDF generation failed on attempt %d/%d: %s", attempt + 1, max_retries, str(e))
                
                if attempt < max_retries - 1:
                    wait_time = retry_delay * (2 ** attempt)  # Exponential backoff
                    logger.info("[create_pdf] Waiting %d seconds before retry", wait_time)
                    await asyncio.sleep(wait_time)
                else:
                    logger.error("[create_pdf] All %d attempts to generate PDF failed", max_retries)
                    raise HTTPException(status_code=500, detail=f"PDF generation failed after {max_retries} attempts: {str(e)}")
        
        if not pdf_reference:
            logger.error("[create_pdf] No PDF reference received from PDF service after %d attempts", max_retries)
            raise HTTPException(status_code=500, detail="PDF generation failed: No reference received after multiple attempts")

        logger.info("[create_pdf] Fetching PDF from MinIO", {"pdfReference": pdf_reference, "filename": filename})

        # Fetch PDF from MinIO with retry logic (handles eventual consistency/transient errors)
        minio_max_retries = 5
        minio_retry_delay = 2  # seconds
        pdf_result = None

        for attempt in range(minio_max_retries):
            try:
                logger.info("[create_pdf] Attempting to load PDF from MinIO - attempt %d/%d", attempt + 1, minio_max_retries)
                pdf_result = await load_generated_pdf(pdf_reference)

                if pdf_result:
                    logger.info("[create_pdf] PDF fetched from MinIO successfully on attempt %d", attempt + 1)
                    break
                else:
                    logger.warning("[create_pdf] PDF not yet available in MinIO on attempt %d/%d", attempt + 1, minio_max_retries)
            except Exception as e:
                logger.warning("[create_pdf] Error fetching PDF from MinIO on attempt %d/%d: %s", attempt + 1, minio_max_retries, str(e))

            if attempt < minio_max_retries - 1:
                wait_time = minio_retry_delay * (2 ** attempt)  # exponential backoff
                logger.info("[create_pdf] Waiting %d seconds before retrying MinIO fetch", wait_time)
                await asyncio.sleep(wait_time)

        if not pdf_result:
            logger.error("[create_pdf] PDF not found in MinIO after %d attempts (reference=%s)", minio_max_retries, pdf_reference)
            raise HTTPException(status_code=404, detail=f"Generated PDF not found or expired after {minio_max_retries} attempts")

        pdf_bytes, stored_filename = pdf_result
        final_filename = stored_filename or filename

        logger.info("[create_pdf] Streaming PDF to client", {
            "pdfReference": pdf_reference,
            "filename": final_filename,
            "sizeBytes": len(pdf_bytes)
        })

        response = Response(
            content=pdf_bytes,
            media_type="application/pdf",
            headers={
                "Content-Disposition": f'attachment; filename="{final_filename}"',
                "Content-Length": str(len(pdf_bytes))
            }
        )

        # Delete PDF after successful response creation (configurable)
        if DELETE_PDFS_AFTER_DOWNLOAD:
            try:
                await delete_temp_pdf_data(pdf_reference)
                await delete_generated_pdf(pdf_reference)
                logger.info("[create_pdf] PDF deleted after successful download", {"pdfReference": pdf_reference})
            except Exception as e:
                logger.warning("[create_pdf] Failed to delete PDF after download", {"pdfReference": pdf_reference, "error": str(e)})

        return response

    except HTTPException as e:
        # Delete PDF on error if it exists (configurable)
        if DELETE_PDFS_AFTER_DOWNLOAD and 'pdf_reference' in locals():
            try:
                await delete_temp_pdf_data(pdf_reference)
                await delete_generated_pdf(pdf_reference)
                logger.info("[create_pdf] PDF deleted after error", {"pdfReference": pdf_reference})
            except Exception as del_e:
                logger.warning("[create_pdf] Failed to delete PDF after error", {"pdfReference": pdf_reference, "error": str(del_e)})
        raise
    except Exception as e:
        # Delete PDF on error if it exists (configurable)
        if DELETE_PDFS_AFTER_DOWNLOAD and 'pdf_reference' in locals():
            try:
                await delete_temp_pdf_data(pdf_reference)
                await delete_generated_pdf(pdf_reference)
                logger.info("[create_pdf] PDF deleted after error", {"pdfReference": pdf_reference})
            except Exception as del_e:
                logger.warning("[create_pdf] Failed to delete PDF after error", {"pdfReference": pdf_reference, "error": str(del_e)})
        logger.error("[create_pdf] Unexpected error during PDF generation", {"error": str(e)})
        raise HTTPException(status_code=500, detail=f"PDF generation failed: {str(e)}")
