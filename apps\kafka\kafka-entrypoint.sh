
#!/bin/bash
set -e

/opt/bitnami/scripts/kafka/setup.sh

#echo "ZZZZZ Waiting for kafka....${KAFKA_KRAFT_CLUSTER_ID}"
#
#CLUSTER_ID=${KAFKA_KRAFT_CLUSTER_ID:-"my-cluster-id"}
#echo "ZZZZZ Waiting for kafka....${CLUSTER_ID}"
#KAFKA_STORAGE_DIR="/bitnami/kafka/data"
#KAFKA_CONFIG="/opt/bitnami/kafka/config/server.properties"
#
#if [ ! -f "$KAFKA_STORAGE_DIR/meta.properties" ]; then
#  echo "Formatting storage directory with cluster ID $CLUSTER_ID"
#  /opt/bitnami/kafka/bin/kafka-storage.sh format \
#    --ignore-formatted \
#    --cluster-id "$CLUSTER_ID" \
#    --config "$KAFKA_CONFIG"
#fi

/opt/bitnami/scripts/kafka/run.sh &

until kafka-topics.sh --bootstrap-server kafka:9092 --list &>/dev/null; do
  sleep 1
done

# Partitions configuration (can be overridden via environment variables)
# By default, requests topic partitions match MAX_REPLICAS to enable full parallelism
MAX_REPLICAS_DEFAULT=${MAX_REPLICAS:-8}
PDF_REQ_PARTITIONS=${PDF_REQ_PARTITIONS:-${MAX_REPLICAS_DEFAULT}}
PDF_RES_PARTITIONS=${PDF_RES_PARTITIONS:-3}
PDF_LOG_PARTITIONS=${PDF_LOG_PARTITIONS:-3}

# echo "creating topic"
kafka-topics.sh --create --if-not-exists --topic reports --bootstrap-server kafka:9092 --partitions 1 --replication-factor 1

# Create processed-reports topic
kafka-topics.sh --create --if-not-exists --topic processed-reports --bootstrap-server kafka:9092 --partitions 1 --replication-factor 1

# PDF related topics
kafka-topics.sh --create --if-not-exists --topic pdf-generation-requests --bootstrap-server kafka:9092 --partitions ${PDF_REQ_PARTITIONS} --replication-factor 1
kafka-topics.sh --create --if-not-exists --topic pdf-generation-results --bootstrap-server kafka:9092 --partitions ${PDF_RES_PARTITIONS} --replication-factor 1
kafka-topics.sh --create --if-not-exists --topic pdf-generation-logs --bootstrap-server kafka:9092 --partitions ${PDF_LOG_PARTITIONS} --replication-factor 1

# Ensure partitions are at least the desired values (safe if equal or larger)
kafka-topics.sh --alter --topic pdf-generation-requests --bootstrap-server kafka:9092 --partitions ${PDF_REQ_PARTITIONS} || true
kafka-topics.sh --alter --topic pdf-generation-results --bootstrap-server kafka:9092 --partitions ${PDF_RES_PARTITIONS} || true
kafka-topics.sh --alter --topic pdf-generation-logs --bootstrap-server kafka:9092 --partitions ${PDF_LOG_PARTITIONS} || true

echo "Kafka Started With topics: reports | processed-reports | pdf-generation-requests | pdf-generation-results | pdf-generation-logs"

wait