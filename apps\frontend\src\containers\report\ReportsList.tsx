import { CardFactory } from "~/containers/report/CardFactory";
import { ReportData, ReportMetadata, ReportSection } from "~/types/global";
import { FolderData } from "root/domain/entities/folder.model";
import { AnimatedFilledButton, Button, Icon } from "@snap/design-system";
import { Plus } from "lucide-react";
import MasonryLayout from "react-layout-masonry";
import { useEffect, useRef, useState } from "react";
import { useReportCRUD } from "~/hooks/useReportCRUD";
import { useReportListActions, useReportListLoadMore } from "~/store/reportListStore";
import EmptyList from "~/components/EmptyList";
import { usePermissionCheck } from "~/components/router/PermissionGuard";
import { Permission } from "~/helpers/permissions.helper";
import { ReportModel } from "root/domain/entities/report.model";
import { FolderModel } from "root/domain/entities/folder.model";
import { toast } from "sonner";
import { useUserData } from "~/store/userStore";
import { useEncryption } from "~/hooks/useEncryption";
import { REPORT_CONSTANTS, REPORT_SECTIONS } from "~/helpers/constants";
import { fetchReportById } from "~/services/gateways/report.gateway"
import { decryptReportPayload } from "~/helpers/encryption.helper"
import { createErrorHandler, getProfileImageFromSection } from "~/helpers";

interface ReportListProps {
  list: (ReportData | FolderData)[];
  isFetched: boolean;
  onNewReport: () => void;
}

export default function ReportsList({
  list,
  onNewReport,
  isFetched,
}: ReportListProps) {
  const { checkPermission } = usePermissionCheck();
  const canCreateReport = checkPermission(Permission.CREATE_REPORT);
  const { invalidateAllReports, generatePDFMutation } = useReportCRUD(null, false);
  const { incrementPage } = useReportListActions();
  const loadMore = useReportListLoadMore();
  const [showLoadMore, setShowLoadMore] = useState(false);
  const [downloadingReports, setDownloadingReports] = useState<Set<string>>(new Set());
  const sectionRef = useRef<HTMLElement>(null);
  const userData = useUserData();
  const organizationLogo = userData?.organization_logo as string;
  const shouldPrintSnapLogo = userData?.print_snap_logo as boolean;
  const { decryptData } = useEncryption();
  const columnsBreakpoints = {
    346: 1, // 282 + 64 (padding)
    628: 2, // (282 * 2) + 20 + 64
    930: 3, // (282 * 3) + (20 * 2) + 64
    1232: 4, // (282 * 4) + (20 * 3) + 64
    1534: 5, // (282 * 5) + (20 * 4) + 64
    1836: 6, // (282 * 6) + (20 * 5) + 64
    2138: 7, // (282 * 7) + (20 * 6) + 64
    2440: 8, // (282 * 8) + (20 * 7) + 64
    2742: 9, // (282 * 9) + (20 * 8) + 64
  };

  useEffect(() => {
    const section = sectionRef.current;
    if (section) {
      section.addEventListener("scroll", handleScroll);
      return () => section.removeEventListener("scroll", handleScroll);
    }
  }, [loadMore]);

  const loadMoreData = async () => {
    incrementPage()
  }

  const handleLoadMore = async () => {
    console.log("load more");
    await loadMoreData();
    invalidateAllReports();
  };

  const handleScroll = () => {
    if (!sectionRef.current) return;

    const { scrollTop, scrollHeight, clientHeight } = sectionRef.current;
    const isAtBottom = scrollTop + clientHeight >= scrollHeight - 100;
    const isAtTop = scrollTop === 0;
    setShowLoadMore(isAtBottom && !isAtTop);
  };

  const handleDownloadPDF = async (reportId: string) => {
    let toastId = null;
    try {
      setDownloadingReports(prev => new Set(prev).add(reportId));
      toastId = toast.loading("Gerando PDF...", {
        description: "Isso pode levar alguns minutos. Por favor, não feche esta aba até o download terminar.",
      });

      const reportDetailsData = await fetchReportById(reportId);
      const decryptedData = await decryptReportPayload(reportDetailsData as ReportData, decryptData);
      const { data: dataMap, ...meta } = decryptedData;
      const reportType = meta?.[REPORT_CONSTANTS.new_report.report_type] as string;
      const foundSections: ReportSection[] = dataMap?.[reportType as keyof typeof dataMap] || [];
      const filteredSections = foundSections.filter((sec) => sec.is_deleted !== true && sec.data_count > 0);
      const imagensSection = filteredSections.find(
        (section) => section.title === REPORT_SECTIONS.imagens
      );
      const profileImage = getProfileImageFromSection(imagensSection);

      const blobUrl = await generatePDFMutation.mutateAsync({
        sections: filteredSections,
        metadata: meta as ReportMetadata,
        profile_image: profileImage,
        should_print_snap_logo: shouldPrintSnapLogo,
        organization_logo: organizationLogo,
      });

      const reportName = meta?.[REPORT_CONSTANTS.new_report.report_name] as string || "relatorio";
      const link = document.createElement("a");
      link.href = blobUrl;
      link.download = `${reportName}.pdf`;
      link.click();
      URL.revokeObjectURL(blobUrl);

      toast.dismiss(toastId);
      toast.success("PDF gerado com sucesso!");
      setDownloadingReports(prev => {
        const newSet = new Set(prev);
        newSet.delete(reportId);
        return newSet;
      });
    } catch (err) {
      console.error("Error in download process:", err);
      if (toastId) toast.dismiss(toastId);
      setDownloadingReports(prev => {
        const newSet = new Set(prev);
        newSet.delete(reportId);
        return newSet;
      });
      createErrorHandler(
        "Ocorreu um erro ao tentar gerar o PDF",
        "Erro ao gerar PDF"
      )(err);
    }
  };

  return (
    <section
      ref={sectionRef}
      className="flex-1 w-full overflow-y-auto overflow-x-hidden min-h-0 max-h-[calc(100vh-232px)] px-8 pt-2 pb-8"
    >
      <MasonryLayout columns={columnsBreakpoints} gap={20} className="relative">
        {canCreateReport ? (
          <AnimatedFilledButton
            data-testid="button-new-report"
            onClick={onNewReport}
            icon={<Icon src="/icons/icone_bigplus.svg" />}
          >
            <div className="flex flex-col items-start">
              <p className="text-2xl">Clique para criar:</p>
              <p className="text-2xl font-bold">Novo relatório</p>
              <p className="text-2xl font-bold">ou pasta</p>
              <div className="justify-self-end">
                <Plus size="20%" />
              </div>
            </div>
          </AnimatedFilledButton>
        ) : null}
        {isFetched &&
          list?.length > 0 &&
          list.map((item, index) => {
            const isFolder = 'folder_id' in item;
            const model = isFolder
              ? new FolderModel(item as FolderData)
              : new ReportModel(item as ReportData);
            return (
              <div className="z-[1]" key={index}>
                <CardFactory 
                  item={model} 
                  onDownloadPDF={handleDownloadPDF}
                  isDownloadingPDF={downloadingReports.has(model.id)}
                />
              </div>
            );
          })}
      </MasonryLayout>
      {isFetched && list?.length === 0 && (
        <EmptyList onReload={() => { }} />
      )}

      {showLoadMore && loadMore && (
        <div className="w-full absolute bottom-2 left-0 flex justify-center py-4 px-8 z-[9999]">
          <Button
            variant="default"
            className="w-full uppercase"
            onClick={handleLoadMore}
          >
            Carregar mais resultados
          </Button>
        </div>
      )}
    </section>
  );
}
