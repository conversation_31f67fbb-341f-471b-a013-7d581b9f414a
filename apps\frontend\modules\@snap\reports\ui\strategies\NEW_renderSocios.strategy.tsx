import React, { useMemo } from "react";
import { ArrayRenderStrategy } from "./RenderStrategy";
import { CustomGridContainer } from "../components/CustomGridContainer";
import { CustomGridItem } from "../components/CustomGridItem";
import { parseValue, translatePropToLabel } from "../../helpers";
import { useReportActions, useReportMode, useReportSections } from "../../context/ReportContext";
import { MdOutlineSubdirectoryArrowRight } from "react-icons/md";
import { CustomReadOnlyInputField } from "../components/CustomReadOnlyInput";
import { renderSourceTooltip } from "./helpers.strategy";
import { ReportsCustomLabel } from "../components/ReportsCustomLabel";
import { Socio } from "../../model/Socios";
import { SocioDTO } from "../../dtos/SocioDTO";

export function useRenderSocios(
  sectionTitle: string
): ArrayRenderStrategy<Socio> {
  const actions = useReportActions();
  const mode = useReportMode();
  const isTrash = mode === "trash";

  const section = useReportSections().find((s) => s.title === sectionTitle);
  const idxMap = useMemo(() => {
    const m = new WeakMap<Socio, number>();
    section?.data.forEach((e, i) => m.set(e as any, i));
    return m;
  }, [section]);

  const includeKey = (isDeleted: boolean) =>
    isTrash ? isDeleted : !isDeleted;

  const testNomeCompletoDeleted = (e: any) => e.nome_completo?.is_deleted === true;
  const testRazaoSocialDeleted = (e: any) => e.razao_social?.is_deleted === true;
  const testDetalhesDeleted = (e: any) =>
    e.detalhes
      ? Object.values(e.detalhes).every((v: any) => v.is_deleted === true)
      : false;
  const testEmpresaDeleted = (e: any) =>
    e.empresa
      ? e.empresa.every((p: any) =>
        Object.values(p.value || {}).every((v: any) => v.is_deleted === true)
      )
      : true;

  const testEntryDeleted = (entry: any): boolean => {
    const isNomeCompletoDeleted = testNomeCompletoDeleted(entry);
    const isRazaoSocialDeleted = testRazaoSocialDeleted(entry);
    const areDetalhesDeleted = testDetalhesDeleted(entry);
    const areEmpresaDeleted = testEmpresaDeleted(entry);

    return (isNomeCompletoDeleted || isRazaoSocialDeleted) && areDetalhesDeleted && areEmpresaDeleted;
  };

  const testSectionDeleted = (section: any): boolean =>
    Array.isArray(section.data) && section.data.every(testEntryDeleted);

  const calculateDataCount = (section: any): number => {
    if (!Array.isArray(section.data)) return 0;

    return section.data.reduce((count: number, entry: any) => {
      const isEntryDeleted = testEntryDeleted(entry);
      return isEntryDeleted ? count : count + 1;
    }, 0);
  };

  const shouldIncludeNestedBlock = (item: any) => {
    const vals = Object.values(item.value || {});
    return isTrash
      ? vals.some((v: any) => v.is_deleted === true)
      : vals.some((v: any) => v.is_deleted === false);
  };

  const shouldIncludeList = (arrayItems: any[]) => {
    if (!Array.isArray(arrayItems)) return false;
    return arrayItems.some((item: any) => shouldIncludeNestedBlock(item));
  };

  const onToggleNestedField = (entryIdx: number, arrayKey: string, blockIdx: number, fieldKey: string) => {
    actions.updateSectionEntries!(
      sectionTitle,
      (e: any, i?: number) => {
        if (i === entryIdx) {
          const item = e[arrayKey]?.[blockIdx];
          if (item?.value?.[fieldKey]) {
            item.value[fieldKey].is_deleted = !item.value[fieldKey].is_deleted;

            const allFieldsDeleted = Object.values(item.value).every((campo: any) =>
              campo.is_deleted === true
            );

            item.is_deleted = allFieldsDeleted;
          }
        }
      },
      testEntryDeleted,
      testSectionDeleted,
      calculateDataCount
    );
  };

  const onToggleNestedBlock = (entryIdx: number, arrayKey: string, blockIdx: number) => {
    actions.updateSectionEntries!(
      sectionTitle,
      (e: any, i?: number) => {
        if (i === entryIdx) {
          const item = e[arrayKey]?.[blockIdx];
          if (item?.value) {
            const targetDeletedState = isTrash ? false : true;

            item.is_deleted = targetDeletedState;

            Object.values(item.value).forEach((campo: any) => {
              if (campo) {
                campo.is_deleted = targetDeletedState;
              }
            });
          }
        }
      },
      testEntryDeleted,
      testSectionDeleted,
      calculateDataCount
    );
  };

  const onToggleListTitle = (entryIdx: number, arrayKey: string) => {
    actions.updateSectionEntries!(
      sectionTitle,
      (e: any, i?: number) => {
        if (i === entryIdx) {
          const arrayItems = e[arrayKey];
          if (arrayItems && Array.isArray(arrayItems)) {
            const targetDeletedState = isTrash ? false : true;

            arrayItems.forEach((item: any) => {
              item.is_deleted = targetDeletedState;
              if (item.value) {
                Object.values(item.value).forEach((campo: any) => {
                  if (campo) {
                    campo.is_deleted = targetDeletedState;
                  }
                });
              }
            });
          }
        }
      },
      testEntryDeleted,
      testSectionDeleted,
      calculateDataCount
    );
  };

  const formatByKey: Record<
    string,
    (entry?: Socio) => React.ReactElement | null
  > = {
    nome_completo: (entry) => {
      if (!entry?.nome_completo || !includeKey(entry.nome_completo.is_deleted || false)) return null;
      const idx = idxMap.get(entry)!;

      return (
        <CustomGridContainer cols={1} key={`nome-completo-${idx}`}>
          <CustomGridItem
            fullWidth
            className="mb-6"
            onToggleField={() =>
              actions.updateSectionEntries!(
                sectionTitle,
                (e: any, i?: number) => {
                  if (i === idx && e.nome_completo) {
                    e.nome_completo.is_deleted = !e.nome_completo.is_deleted;

                    const targetDeletedState = e.nome_completo.is_deleted;

                    if (e.detalhes) {
                      Object.values(e.detalhes).forEach((detalhe: any) => {
                        if (detalhe) detalhe.is_deleted = targetDeletedState;
                      });
                    }

                    if (e.empresa) {
                      e.empresa.forEach((p: any) => {
                        p.is_deleted = targetDeletedState;
                        if (p.value) {
                          Object.values(p.value).forEach((campo: any) => {
                            if (campo) campo.is_deleted = targetDeletedState;
                          });
                        }
                      });
                    }
                  }
                },
                testEntryDeleted,
                testSectionDeleted,
                calculateDataCount
              )
            }
          >
            <CustomReadOnlyInputField
              label={entry.nome_completo.label.toUpperCase()}
              colorClass="bg-primary"
              labelTextClass="text-accent"
              value={parseValue(entry.nome_completo.value)}
              tooltip={renderSourceTooltip(entry.nome_completo.source)}
            />
          </CustomGridItem>
        </CustomGridContainer>
      );
    },

    razao_social: (entry) => {
      if (!entry?.razao_social || !includeKey(entry.razao_social.is_deleted || false)) return null;
      const idx = idxMap.get(entry)!;

      return (
        <CustomGridContainer cols={1} key={`razao-social-${idx}`}>
          <CustomGridItem
            fullWidth
            className="mb-6"
            onToggleField={() =>
              actions.updateSectionEntries!(
                sectionTitle,
                (e: any, i?: number) => {
                  if (i === idx && e.razao_social) {
                    e.razao_social.is_deleted = !e.razao_social.is_deleted;

                    const targetDeletedState = e.razao_social.is_deleted;

                    if (e.detalhes) {
                      Object.values(e.detalhes).forEach((detalhe: any) => {
                        if (detalhe) detalhe.is_deleted = targetDeletedState;
                      });
                    }

                    if (e.empresa) {
                      e.empresa.forEach((p: any) => {
                        p.is_deleted = targetDeletedState;
                        if (p.value) {
                          Object.values(p.value).forEach((campo: any) => {
                            if (campo) campo.is_deleted = targetDeletedState;
                          });
                        }
                      });
                    }
                  }
                },
                testEntryDeleted,
                testSectionDeleted,
                calculateDataCount
              )
            }
          >
            <CustomReadOnlyInputField
              label={entry.razao_social.label.toUpperCase()}
              colorClass="bg-primary"
              labelTextClass="text-accent"
              value={parseValue(entry.razao_social.value)}
              tooltip={renderSourceTooltip(entry.razao_social.source)}
            />
          </CustomGridItem>
        </CustomGridContainer>
      );
    },

    detalhes: (entry) => {
      if (!entry?.detalhes) return null;
      const subs = Object.entries(entry.detalhes).filter(([, v]) =>
        includeKey((v as any).is_deleted)
      );
      if (!subs.length) return null;
      const idx = idxMap.get(entry)!;

      return (
        <CustomGridContainer
          cols={2}
          columnFirst
          key={`detalhes-${idx}`}
          className="mb-6"
        >
          {subs.map(([fieldKey, val]) => (
            <CustomGridItem
              key={fieldKey}
              cols={1}
              onToggleField={() =>
                actions.updateSectionEntries!(
                  sectionTitle,
                  (e: any, i?: number) => {
                    if (i === idx) {
                      const d = e.detalhes?.[fieldKey];
                      if (d) d.is_deleted = !d.is_deleted;
                    }
                  },
                  testDetalhesDeleted,
                  testSectionDeleted,
                  calculateDataCount
                )
              }
            >
              <CustomReadOnlyInputField
                label={translatePropToLabel((val as any).label || fieldKey).toUpperCase()}
                value={parseValue(String((val as any).value))}
                tooltip={renderSourceTooltip((val as any).source)}
              />
            </CustomGridItem>
          ))}
        </CustomGridContainer>
      );
    },

    empresa: (entry) => {
      if (!entry?.empresa?.length || !shouldIncludeList(entry.empresa)) return null;
      const idx = idxMap.get(entry)!;

      const blocks = entry.empresa
        .map((p, i) => ({ bloco: p, idx: i }))
        .filter(({ bloco }) => shouldIncludeNestedBlock(bloco));

      if (!blocks.length) return null;

      return (
        <CustomGridContainer cols={1} key={`empresa-${idx}`} className="mb-4">
          <CustomGridItem
            cols={1}
            containerClassName="w-fit pr-12"
            onToggleField={() => onToggleListTitle(idx, 'empresa')}
          >
            <ReportsCustomLabel
              label="EMPRESAS"
              colorClass="bg-primary"
            />
          </CustomGridItem>
          {blocks.map(({ bloco, idx: origIdx }, blockRenderIdx) => (
            <div key={`empresa-${idx}-${origIdx}`} className="mb-4">
              <CustomGridItem
                cols={1}
                containerClassName="w-fit pr-12 pb-2"
                onToggleField={() => onToggleNestedBlock(idx, 'empresa', origIdx)}
              >
                <ReportsCustomLabel
                  label={`EMPRESA ${!isTrash ? blockRenderIdx + 1 : ""}`}
                  colorClass="bg-border"
                />
              </CustomGridItem>
              <div className="pl-5">
                <CustomGridContainer cols={2}>
                  {Object.entries(bloco.value)
                    .filter(([_, v]: any) =>
                      isTrash ? v.is_deleted : !v.is_deleted
                    )
                    .map(([fieldKey, fieldValue]: any, index) => (
                      <CustomGridItem
                        key={`empresa-${idx}-${origIdx}-${fieldKey}`}
                        cols={1}
                        className="py-1"
                        onToggleField={() => onToggleNestedField(idx, 'empresa', origIdx, fieldKey)}
                      >
                        <CustomReadOnlyInputField
                          label={translatePropToLabel(
                            fieldValue.label || fieldKey
                          ).toUpperCase()}
                          colorClass="bg-border"
                          value={String(fieldValue.value || "")}
                          isFirstLabelList={index === 0}
                          icon={index === 0 ? <MdOutlineSubdirectoryArrowRight size={16} /> : null}
                          tooltip={renderSourceTooltip(fieldValue.source)}
                        />
                      </CustomGridItem>
                    ))}
                </CustomGridContainer>
              </div>
            </div>
          ))}
        </CustomGridContainer>
      );
    },
  };

  const validateKeys = (keys: Array<keyof Socio>): boolean => {
    return keys.some((campo) => campo in formatByKey);
  };

  const renderSingleItem = (entry: Socio): React.ReactElement[] => {
    // Validate entry using SocioDTO
    if (!SocioDTO.validate(entry)) {
      console.warn("[Seção Sócios] Formato de dados inválido:", entry);
      return [];
    }

    const keys = Object.keys(entry) as Array<keyof Socio>;

    if (!validateKeys(keys)) {
      console.warn("[Seção Sócios] Chaves inválidas:", keys);
      return [];
    }

    const orderedKeys: Array<keyof Socio> = [
      'nome_completo',
      'razao_social',
      'detalhes',
      'empresa'
    ];

    const filteredKeys = orderedKeys.filter(key => keys.includes(key));

    return filteredKeys
      .map((chave) => formatByKey[chave]?.(entry))
      .filter((el): el is React.ReactElement => el !== null);
  };

  const render = (dataArray: any[]): React.ReactElement[] => {
    if (!Array.isArray(dataArray)) {
      console.warn("[Seção Sócios] Expected array but received:", typeof dataArray);
      return [];
    }

    // Validate and transform data using SocioDTO
    if (!SocioDTO.validateArray(dataArray)) {
      console.warn("[Seção Sócios] Invalid data format:", dataArray);
      return [];
    }

    const validData = SocioDTO.transformArray(dataArray);

    const filteredData = validData.filter((entry) => {
      if (isTrash) {
        return testNomeCompletoDeleted(entry) ||
          testRazaoSocialDeleted(entry) ||
          testDetalhesDeleted(entry) ||
          testEmpresaDeleted(entry) ||
          (entry.detalhes && Object.values(entry.detalhes).some((v: any) => v.is_deleted === true)) ||
          (entry.empresa && entry.empresa.some((p: any) =>
            Object.values(p.value || {}).some((v: any) => v.is_deleted === true)
          ));
      } else {
        const isCompletelyDeleted = testEntryDeleted(entry);
        return !isCompletelyDeleted;
      }
    });

    if (filteredData.length === 0) {
      return [];
    }

    const allElements: React.ReactElement[] = [];

    filteredData.forEach((entry, index) => {
      const elements = renderSingleItem(entry);

      if (elements.length === 0) {
        return;
      }

      if (filteredData.length > 1) {
        const isLastItem = index === filteredData.length - 1;
        allElements.push(
          <div
            key={`socio-${index}`}
            className={`relative mb-8 pb-1 ${isLastItem ? '!mb-0 !pb-0' : ''}`}
          >
            {elements}
            {!isLastItem && (
              <div
                className="absolute bottom-0 border-b-2 border-dashed border-neutral-100"
                style={{ left: '-20px', right: '-20px' }}
              />
            )}
          </div>
        );
      } else {
        allElements.push(...elements);
      }
    });

    return allElements;
  };

  const deleteSectionEntries = () => {
    const updater = actions.updateSectionEntries;
    if (!updater) return;

    const targetDeletedState = mode !== "trash";

    updater(
      sectionTitle,
      entry => {
        if (entry.nome_completo) {
          entry.nome_completo.is_deleted = targetDeletedState;
        }
        if (entry.razao_social) {
          entry.razao_social.is_deleted = targetDeletedState;
        }
        if (entry.detalhes) {
          Object.values(entry.detalhes).forEach((detalhe: any) => {
            if (detalhe) detalhe.is_deleted = targetDeletedState;
          });
        }
        if (entry.empresa) {
          entry.empresa.forEach((p: any) => {
            p.is_deleted = targetDeletedState;
            if (p.value) {
              Object.values(p.value).forEach((campo: any) => {
                if (campo) campo.is_deleted = targetDeletedState;
              });
            }
          });
        }
      },
      testEntryDeleted,
      testSectionDeleted,
      calculateDataCount
    );
  };

  return useMemo(() => ({
    validateKeys,
    formatByKey,
    render,
    testEntryDeleted,
    testSectionDeleted,
    deleteSectionEntries,
  }), [
    validateKeys,
    formatByKey,
    render,
    testEntryDeleted,
    testSectionDeleted,
    deleteSectionEntries,
  ]);
}