import React from 'react';
import { View, Text, StyleSheet, Svg, Rect } from '../pdf-components';
import { ReportSection } from '../../global';
import { translatePropToLabel, getSingular, translateSource } from '../../helpers';
import { Socio } from '../../dtos/socios/SocioValidator';
import { SocioDTO } from '../../dtos/socios/SocioDTO';

interface RenderPrintSociosProps {
  section: Omit<ReportSection, 'data'> & {
    data: Array<Socio>
  };
}

export const RenderPrintSocios: React.FC<RenderPrintSociosProps> = ({ section }) => {
  if (!section.data?.length) return null;

  const validData = SocioDTO.processData(section.data);
  
  if (validData.length === 0) {
    console.warn("[RenderPrintSocios] No valid data entries found, skipping section");
    return null;
  }

  return (
    <View style={styles.container} key={section.title}>
      <View style={styles.sectionTitleContainer} wrap={false}>
        <Svg width={8} height={8} viewBox="0 0 8 8" style={styles.searchIcon}>
          <Rect width="8" height="8" fill='#FE473C' />
        </Svg>
        <Text style={styles.heading}>{section.title}</Text>
      </View>

      {validData.map((socio, socioIndex) => (
        <View key={`socio-${socioIndex}`} style={styles.socioContainer}>
          {/* Nome Completo */}
          {socio.nome_completo && !socio.nome_completo.is_deleted && (
            <View style={styles.nomeContainer} >
              <View style={styles.nomeLabelContainer}>
                <Text style={styles.nomeLabel}>
                  {(socio.nome_completo.label || "Nome").toUpperCase()}
                </Text>
                <Text style={styles.sourceText}>
                  {socio.nome_completo.source?.map((src: string) => `| ${translateSource(src)}\u00A0`).join('')}
                </Text>
              </View>
              <Text style={styles.nomeValue}>{socio.nome_completo.value}</Text>
            </View>
          )}

          {/* Razão Social */}
          {socio.razao_social && !socio.razao_social.is_deleted && (
            <View style={styles.nomeContainer} >
              <View style={styles.nomeLabelContainer}>
                <Text style={styles.nomeLabel}>
                  {(socio.razao_social.label || "Razão Social").toUpperCase()}
                </Text>
                <Text style={styles.sourceText}>
                  {socio.razao_social.source?.map((src: string) => `| ${translateSource(src)}\u00A0`).join('')}
                </Text>
              </View>
              <Text style={styles.nomeValue}>{socio.razao_social.value}</Text>
            </View>
          )}

          {/* Detalhes do Sócio */}
          {socio.detalhes && typeof socio.detalhes === 'object' && (
            <View style={styles.detalhesContainer}>
              <View style={styles.detalhesGrid}>
                {Object.entries(socio.detalhes)
                  .filter(([_, field]) => !field.is_deleted)
                  .map(([key, field], index) => (
                    <View key={`detalhe-${index}`} style={styles.detailsCell}>
                      <View style={styles.infoContainer} wrap={false}>
                        <Svg width={8} height={8} viewBox="0 0 8 8" style={styles.searchIcon}>
                          <Rect width="8" height="8" fill='#CCCCCC' />
                        </Svg>
                        <Text style={styles.label}>{translatePropToLabel(field.label || key).toUpperCase()}</Text>
                        <Text style={styles.sourceText}>
                          {field.source?.map((src: string) => `| ${translateSource(src)}\u00A0`).join('')}
                        </Text>
                      </View>
                      <Text style={styles.value}>{field.value}</Text>
                    </View>
                  ))}
              </View>
            </View>
          )}

          {/* Empresas */}
          {socio.empresa && Array.isArray(socio.empresa) && socio.empresa.length > 0 && (
            <View style={styles.empresaContainer}>
              <View style={styles.subtitleContainer} wrap={false}>
                <Svg width={4} height={4} viewBox="0 0 4 4" style={styles.searchIcon}>
                  <Rect width="4" height="4" fill='#889EA3' />
                </Svg>
                <Text style={styles.subtitle}>EMPRESAS</Text>
              </View>
              <View style={styles.empresaGrid}>
                {socio.empresa
                  .filter(empresa => !empresa.is_deleted && typeof empresa.value === 'object')
                  .map((empresa, index) => (
                    <View key={`empresa-${index}`} style={styles.empresaBlock} >
                      <View style={styles.listContainer} wrap={false}>
                        <Svg width={4} height={4} viewBox="0 0 4 4" style={styles.searchIcon}>
                          <Rect width="4" height="4" fill="#889EA3" />
                        </Svg>
                        <Text style={styles.itemTitle}>
                          {translatePropToLabel(getSingular(empresa.label) || 'EMPRESA').toUpperCase()} {index + 1}
                        </Text>
                        <Text style={styles.sourceText}>
                          {empresa.source?.map((src: string) => `| ${translateSource(src)}\u00A0`).join('')}
                        </Text>
                      </View>
                      <View style={styles.fieldsGrid}>
                        {Object.entries(empresa.value)
                          .filter(([_, field]) => !field.is_deleted)
                          .map(([fieldKey, fieldValue], fieldIndex) => (
                            <View key={`field-${fieldIndex}`} style={styles.cell}>
                              <View style={styles.infoContainer} wrap={false}>
                                <Svg width={8} height={8} viewBox="0 0 8 8" style={styles.searchIcon}>
                                  <Rect width="8" height="8" fill='#CCCCCC' />
                                </Svg>
                                <Text style={styles.label}>{translatePropToLabel(fieldValue.label || fieldKey).toUpperCase()}</Text>
                                <Text style={styles.sourceText}>
                                  {fieldValue.source?.map((src: string) => `| ${translateSource(src)}\u00A0`).join('')}
                                </Text>
                              </View>
                              <Text style={styles.value}>
                                {fieldValue.value}
                              </Text>
                            </View>
                          ))}
                      </View>
                    </View>
                  ))}
              </View>
            </View>
          )}
        </View>
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginTop: 8,
    marginBottom: 16,
  },
  heading: {
    fontSize: 12,
    textTransform: 'uppercase',
    marginBottom: 8,
    fontWeight: 'bold',
  },
  searchIcon: {
    width: 4,
    height: 4,
    marginRight: 4,
    marginTop: 1
  },
  sectionTitleContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    borderBottomWidth: 1,
    borderBottomColor: "#889EA3",
  },
  nomeLabelContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    flexWrap: 'wrap',
    paddingBottom: 2,
  },
  subtitleContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 6,
    flexWrap: 'wrap',
  },
  listContainer: {
    paddingVertical: 8,
    flexDirection: 'row',
    alignItems: 'flex-start',
    borderBottom: '1pt dashed #CCCCCC',
    flexWrap: 'wrap',
  },
  subtitle: {
    fontSize: 10,
    fontWeight: 'bold',
    color: '#889EA3',
    textTransform: 'uppercase',
  },
  socioContainer: {
    marginBottom: 16,
    padding: 8,
    backgroundColor: '#F9F9FA',
    borderBottomLeftRadius: 2,
    borderBottomRightRadius: 2,
  },
  sourceText: {
    paddingLeft: 4,
    paddingBottom: 2,
    fontSize: 8,
    color: '#FE473C',
  },
  nomeContainer: {
    marginBottom: 12,
  },
  nomeLabel: {
    fontSize: 10,
    fontWeight: 'bold',
    color: '#FE473C',
  },
  nomeValue: {
    fontSize: 12,
    fontWeight: 'bold',
  },
  detalhesContainer: {
    marginBottom: 12,
  },
  empresaContainer: {
    marginBottom: 8,
  },
  empresaBlock: {
    width: '50%',
    paddingRight: 8,
    marginBottom: 10,
  },
  itemTitle: {
    fontSize: 10,
    fontWeight: 'bold',
    textTransform: 'uppercase',
  },
  detalhesGrid: {
    paddingLeft: 8,
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  empresaGrid: {
    paddingLeft: 8,
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  detailsCell: {
    width: '50%',
    paddingRight: 8,
    marginBottom: 6,
  },
  cell: {
    width: '100%',
    paddingRight: 8,
    marginBottom: 6,
  },
  label: {
    fontSize: 10,
    fontWeight: 'bold',
    marginBottom: 2,
    color: '#889EA3',
  },
  value: {
    paddingLeft: 8,
    fontSize: 10,
  },
  infoContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    flexWrap: 'wrap',
  },
  fieldsGrid: {
    paddingLeft: 8,
    paddingTop: 6,
  },
});