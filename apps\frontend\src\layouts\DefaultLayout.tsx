import { useState } from "react";
import { Outlet } from "react-router";
import BreadcrumbNavigation from "~/components/Breadcrumbs";
import Header from "~/components/Header";
import Toolbar from "~/components/Toolbar";
import { BreadcrumbItem } from "~/types/global";

const DefaultLayout = () => {
  const [breadcrumbs, setBreadcrumbs] = useState<BreadcrumbItem[]>([]);
  return (
    <div className="h-screen flex flex-col">
      <Header />
      <div className="px-8">
        <BreadcrumbNavigation breadcrumbs={breadcrumbs} />
      </div>
      <main className="flex-1 flex flex-col min-h-0">
        <div className="w-full px-8 py-4">
          <Toolbar />
        </div>
        <div className="flex-1 w-full overflow-auto">
          <Outlet context={{ setBreadcrumbs }} />
        </div>
      </main>
    </div>
  );
};

export default DefaultLayout;
