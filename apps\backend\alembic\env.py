import asyncio
import logging
from logging.config import fileConfig
import os
import sys

from sqlalchemy import pool
from sqlalchemy.engine import Connection
from sqlalchemy.ext.asyncio import async_engine_from_config

from alembic import context

# Add the parent directory to the Python path so we can import our models
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import your models and configuration
from models import Base
from models.user_model import Users
from models.organization_model import Organizations
from models.organization_users_model import OrganizationUsers
from models.invite_model import Invite
from models.report_model import UserReports
from models.user_columns_hmac import UserColumnsHmac
from models.user_report_ledger_model import UserReportLedger
from models.report_executions_model import ReportExecutions
from models.folder_model import Folder
from models.folder_hmac import FolderHmac
from core.config_alembic import settings

# this is the Alembic Config object, which provides
# access to the values within the .ini file in use.
config = context.config

# Interpret the config file for Python logging.
# This line sets up loggers basically.
if config.config_file_name is not None:
    fileConfig(config.config_file_name)

# add your model's MetaData object here
# for 'autogenerate' support
target_metadata = Base.metadata

# other values from the config, defined by the needs of env.py,
# can be acquired:
# my_important_option = config.get_main_option("my_important_option")
# ... etc.

def get_url():
    """Get database URL from settings"""
    # Add detailed logging to debug connection issues
    print(f"DEBUG: DB_HOST = {settings.DB_HOST}")
    print(f"DEBUG: DB_NAME = {settings.DB_NAME}")
    print(f"DEBUG: DB_USER = {settings.DB_USER}")
    print(f"DEBUG: DB_PASS = {'*' * len(settings.DB_PASS) if settings.DB_PASS else 'None'}")
    
    # Check if we're accidentally connecting to Keycloak's database
    # if settings.DB_NAME == "keycloak":
    #     print("⚠️  WARNING: Connecting to Keycloak database instead of application database!")
    #     print("   Expected DB_NAME=snap_reports, but got DB_NAME=keycloak")
    #     print("   Please check your environment variables")
    
    url = settings.DATABASE_URL
    print(f"DEBUG: Database URL = {url.replace(settings.DB_PASS, '*' * len(settings.DB_PASS)) if settings.DB_PASS else url}")
    
    return url

def run_migrations_offline() -> None:
    """Run migrations in 'offline' mode.

    This configures the context with just a URL
    and not an Engine, though an Engine is acceptable
    here as well.  By skipping the Engine creation
    we don't even need a DBAPI to be available.

    Calls to context.execute() here emit the given string to the
    script output.

    """
    url = get_url()
    context.configure(
        url=url,
        target_metadata=target_metadata,
        literal_binds=True,
        dialect_opts={"paramstyle": "named"},
    )

    with context.begin_transaction():
        context.run_migrations()


def do_run_migrations(connection: Connection) -> None:
    context.configure(connection=connection, target_metadata=target_metadata)

    with context.begin_transaction():
        context.run_migrations()


async def run_async_migrations() -> None:
    """In this scenario we need to create an Engine
    and associate a connection with the context.

    """
    configuration = config.get_section(config.config_ini_section)
    configuration["sqlalchemy.url"] = get_url()
    
    connectable = async_engine_from_config(
        configuration,
        prefix="sqlalchemy.",
        poolclass=pool.NullPool,
    )

    async with connectable.connect() as connection:
        await connection.run_sync(do_run_migrations)

    await connectable.dispose()


def run_migrations_online() -> None:
    """Run migrations in 'online' mode.

    In this scenario we need to create an Engine
    and associate a connection with the context.

    """
    asyncio.run(run_async_migrations())


if context.is_offline_mode():
    run_migrations_offline()
else:
    run_migrations_online() 