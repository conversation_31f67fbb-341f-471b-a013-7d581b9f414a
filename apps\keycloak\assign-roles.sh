#!/bin/bash
set -e

# --- Configuration Variables ---
REALM="${KC_REALM:-SnapReportsRealm}"
CLIENT_ID="myclient"
SERVICE_USER="service-account-$CLIENT_ID"
ROLE="realm-admin"
CLIENT_REALM_MANAGEMENT="realm-management"

KEYCLOAK_SERVER="${KEYCLOAK_URL:-http://localhost:8080/}"
ADMIN_USER="${KEYCLOAK_ADMIN:-admin}"
ADMIN_PASSWORD="${KEYCLOAK_ADMIN_PASSWORD:-admin123}"

echo "🚀 Role assignment script started..."
echo "ℹ️ Waiting for Keycloak to become ready..."

# ⏳ Retry loop to wait for Keycloak connection
until /opt/keycloak/bin/kcadm.sh config credentials \
  --server "$KEYCLOAK_SERVER" \
  --realm master \
  --user "$ADMIN_USER" \
  --password "$ADMIN_PASSWORD" > /dev/null 2>&1; do

  echo "⏳ Waiting for Keycloak CLI to connect..."
  sleep 5
done

echo "✅ Connected to Keycloak via kcadm."

# --- Main Logic ---
echo "🔎 Getting ID for service account user '$SERVICE_USER' in realm '$REALM'..."
# Get the ID and remove potential quotes from the CSV output
SERVICE_ACCOUNT_ID=$(/opt/keycloak/bin/kcadm.sh get users -r "$REALM" -q username="$SERVICE_USER" --fields id --format csv --noquotes | tail -n1)

if [ -z "$SERVICE_ACCOUNT_ID" ]; then
  echo "❌ Service account user '$SERVICE_USER' not found in realm '$REALM'."
  echo "ℹ️ Please ensure the client '$CLIENT_ID' exists and has service accounts enabled."
  exit 1
fi

echo "✅ Found Service Account User ID: $SERVICE_ACCOUNT_ID"

echo "🔐 Assigning role '$ROLE' from client '$CLIENT_REALM_MANAGEMENT' to the service account..."
# --- ⬇️ KEY CHANGES ARE HERE ⬇️ ---
# 1. Use '--uid' with the ID we fetched, not '--uusername'.
# 2. Use the correct client ID flag '--cclientid'.
/opt/keycloak/bin/kcadm.sh add-roles \
  --uid "$SERVICE_ACCOUNT_ID" \
  --cclientid "$CLIENT_REALM_MANAGEMENT" \
  --rolename "$ROLE" \
  -r "$REALM"

echo "✅ Role assignment completed for service account of '$CLIENT_ID'."