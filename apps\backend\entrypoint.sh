#!/bin/bash

# Exit immediately if a command exits with a non-zero status.
set -e

# =============================================================================
# CONFIGURATION
# =============================================================================

# Default database configuration
DB_HOST="${DB_HOST:-mystack_postgres}"
DB_NAME="${DB_NAME:-keycloak}"
DB_USER="${DB_USER:-postgres}"
DB_PASS="${DB_PASS:-postgres}"

# =============================================================================
# UTILITY FUNCTIONS
# =============================================================================

print_title() {
    echo ""
    echo "===================="
    echo "$1"
    echo "===================="
}

print_info() {
    echo "ℹ️  $1"
}

print_success() {
    echo "✅ $1"
}

print_warning() {
    echo "⚠️  $1"
}

print_error() {
    echo "❌ $1"
}

# =============================================================================
# DATABASE MIGRATION FUNCTIONS
# =============================================================================

test_database_connection() {
    print_info "Testing database connection..."
    
    # Create Python script to test database connection
    cat > /tmp/test_db_connection.py << 'EOF'
import asyncio
import os
import sys
from sqlalchemy import text
from sqlalchemy.ext.asyncio import create_async_engine

async def test_connection():
    # Get database connection details from environment
    db_host = os.getenv("DB_HOST", "postgres")
    db_name = os.getenv("DB_NAME", "keycloak")
    db_user = os.getenv("DB_USER", "postgres")
    db_pass = os.getenv("DB_PASS", "postgres")
    
    print(f"Testing connection to database '{db_name}' at '{db_host}'")
    
    # Create database URL
    database_url = f"postgresql+asyncpg://{db_user}:{db_pass}@{db_host}:5432/{db_name}"
    
    try:
        # Create engine
        engine = create_async_engine(database_url, echo=False)
        
        async with engine.begin() as conn:
            # Simple test query
            result = await conn.execute(text("SELECT 1 as test"))
            test_value = result.scalar()
            print(f"Connection test successful: {test_value}")
            return True
            
    except Exception as e:
        print(f"Connection test failed: {e}")
        return False
    finally:
        await engine.dispose()

if __name__ == "__main__":
    success = asyncio.run(test_connection())
    sys.exit(0 if success else 1)
EOF

    # Run the connection test
    if python /tmp/test_db_connection.py; then
        print_success "Database connection test successful!"
        return 0
    else
        print_error "Database connection test failed!"
        return 1
    fi
}

run_schema_migration() {
    print_title "Running Schema Migration"
    
    cd /app
    print_info "Changed to /app directory"
    
    # Test database connection first
    if ! test_database_connection; then
        print_error "Cannot connect to database. Aborting migration."
        return 1
    fi
    
    # Create the main migration script
    cat > /tmp/schema_migration.py << 'EOF'
#!/usr/bin/env python3
import sys
import os
import asyncio
import logging
from sqlalchemy import text, inspect, MetaData
from sqlalchemy.ext.asyncio import create_async_engine
from sqlalchemy.schema import CreateTable
from sqlalchemy.dialects import postgresql

# Add the app directory to Python path
sys.path.insert(0, '/app')

# Setup detailed logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s [%(levelname)s]: %(message)s"
)
logger = logging.getLogger(__name__)

try:
    from models.base import Base
    from models import *  # Import all models
    
    # Get database connection details from environment
    db_host = os.getenv("DB_HOST", "postgres")
    db_name = os.getenv("DB_NAME", "keycloak")
    db_user = os.getenv("DB_USER", "postgres")
    db_pass = os.getenv("DB_PASS", "postgres")
    
    # Create database URL
    database_url = f"postgresql+asyncpg://{db_user}:{db_pass}@{db_host}:5432/{db_name}"
    
    async def get_model_enums():
        """Get all enum types from SQLAlchemy models"""
        logger.info("Getting enum types from SQLAlchemy models...")
        
        # Get all enum types from model columns
        model_enums = {}
        for table_name, table in Base.metadata.tables.items():
            logger.info(f"Scanning table '{table_name}' for enum columns...")
            for column in table.columns:
                logger.info(f"  Checking column '{column.name}' with type: {type(column.type).__name__}")
                if hasattr(column.type, 'enums') and hasattr(column.type, 'name'):
                    # This is a PostgreSQL enum column
                    enum_name = column.type.name
                    enum_values = column.type.enums
                    if enum_name not in model_enums:
                        logger.info(f"  Found new model enum: {enum_name} with values: {enum_values}")
                        model_enums[enum_name] = {
                            'name': enum_name,
                            'values': enum_values,
                            'table': table_name,
                            'column': column.name
                        }
                    else:
                        logger.info(f"  Found existing model enum: {enum_name} (already registered)")
                else:
                    logger.info(f"  Column '{column.name}' is not an enum type")
        
        logger.info(f"Total unique model enums found: {len(model_enums)}")
        for enum_name, enum_data in model_enums.items():
            logger.info(f"  - {enum_name}: {enum_data['values']} (used in {enum_data['table']}.{enum_data['column']})")
        return model_enums
    
    async def get_model_tables():
        """Get all tables from SQLAlchemy models"""
        logger.info("Getting tables from SQLAlchemy models...")
        
        # Get all tables from the metadata
        model_tables = {}
        for table_name, table in Base.metadata.tables.items():
            logger.info(f"Found model table: {table_name}")
            model_tables[table_name] = table
        
        logger.info(f"Total model tables: {len(model_tables)}")
        return model_tables
    
    async def get_database_enums(engine):
        """Get all enum types from the database"""
        logger.info("Getting enum types from database...")
        
        db_enums = {}
        async with engine.begin() as conn:
            # Get all enum types in public schema
            result = await conn.execute(text("""
                SELECT t.typname as enum_name,
                       array_agg(e.enumlabel ORDER BY e.enumsortorder) as enum_values
                FROM pg_type t
                JOIN pg_enum e ON t.oid = e.enumtypid
                JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace
                WHERE n.nspname = 'keycloak'
                GROUP BY t.typname;
            """))
            
            for row in result.fetchall():
                enum_name = row[0]
                enum_values = row[1]
                db_enums[enum_name] = enum_values
                logger.info(f"Found database enum: {enum_name} with values: {enum_values}")
        
        logger.info(f"Total database enums found: {len(db_enums)}")
        if db_enums:
            for enum_name, enum_values in db_enums.items():
                logger.info(f"  - {enum_name}: {enum_values}")
        else:
            logger.info("  No existing enums found in database")
        return db_enums
    
    async def get_database_tables(engine):
        """Get all tables from the database"""
        logger.info("Getting tables from database...")
        
        db_tables = {}
        async with engine.begin() as conn:
            # Get all tables in public schema
            result = await conn.execute(text("""
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_schema = 'public' 
                AND table_type = 'BASE TABLE'
                ORDER BY table_name;
            """))
            
            for row in result.fetchall():
                table_name = row[0]
                # Store both with and without schema prefix for comparison
                db_tables[table_name] = table_name
                db_tables[f"public.{table_name}"] = table_name
                logger.info(f"Found database table: {table_name}")
        
        logger.info(f"Total database tables: {len(db_tables)}")
        return db_tables
    
    async def get_table_columns(engine, table_name):
        """Get all columns from a specific table"""
        logger.info(f"Getting columns for table: {table_name}")
        
        async with engine.begin() as conn:
            result = await conn.execute(text("""
                SELECT 
                    column_name,
                    data_type,
                    is_nullable,
                    column_default,
                    character_maximum_length,
                    numeric_precision,
                    numeric_scale
                FROM information_schema.columns 
                WHERE table_schema = 'public' 
                AND table_name = :table_name
                ORDER BY ordinal_position;
            """), {"table_name": table_name})
            
            columns = {}
            for row in result.fetchall():
                column_name = row[0]
                columns[column_name] = {
                    'name': column_name,
                    'type': row[1],
                    'nullable': row[2] == 'YES',
                    'default': row[3],
                    'max_length': row[4],
                    'precision': row[5],
                    'scale': row[6]
                }
                logger.info(f"  Found column: {column_name} ({row[1]}, nullable: {row[2] == 'YES'})")
        
        return columns
    
    async def create_enum(engine, enum_name, enum_data):
        """Create a new enum type"""
        logger.info(f"Creating enum: {enum_name}")
        
        # Get enum values from the enum data
        enum_values = enum_data['values']
        logger.info(f"Enum {enum_name} values: {enum_values}")
        
        # Create the enum values list for PostgreSQL
        enum_values_list = ", ".join([f"'{value}'" for value in enum_values])
        create_enum_stmt = f"CREATE TYPE {enum_name} AS ENUM ({enum_values_list})"
        
        logger.info(f"Executing: {create_enum_stmt}")
        
        async with engine.begin() as conn:
            try:
                # Execute the CREATE TYPE statement
                await conn.execute(text(create_enum_stmt))
                logger.info(f"Successfully created enum: {enum_name}")
            except Exception as e:
                # Catch DuplicateObjectError from asyncpg or similar
                if "already exists" in str(e):
                    logger.warning(f"Enum {enum_name} already exists, skipping creation.")
                else:
                    logger.error(f"Failed to create enum {enum_name}: {e}")
                    raise
    
    async def create_table(engine, table_name, table):
        """Create a new table"""
        logger.info(f"Creating table: {table_name}")
        
        # Generate CREATE TABLE statement with proper PostgreSQL dialect
        create_stmt = CreateTable(table).compile(dialect=engine.dialect)
        
        async with engine.begin() as conn:
            # Execute the CREATE TABLE statement
            await conn.execute(text(str(create_stmt)))
            logger.info(f"Successfully created table: {table_name}")
    
    async def add_column(engine, table_name, column, model_table):
        """Add a new column to an existing table"""
        logger.info(f"Adding column {column.name} to table {table_name}")
        
        # Generate the column definition with proper PostgreSQL dialect
        column_type = column.type.compile(dialect=engine.dialect)
        
        # Handle default values and server defaults
        default_clause = ""
        server_default_clause = ""
        default_value_for_update = None
        
        # Check for server_default first (this is what PostgreSQL uses)
        if hasattr(column, 'server_default') and column.server_default is not None:
            logger.info(f"Column {column.name} has server_default: {column.server_default}")
            if hasattr(column.server_default, 'arg'):
                server_default_value = column.server_default.arg
                if isinstance(server_default_value, str):
                    server_default_clause = f" DEFAULT '{server_default_value}'"
                    default_value_for_update = f"'{server_default_value}'"
                else:
                    server_default_clause = f" DEFAULT {server_default_value}"
                    default_value_for_update = str(server_default_value)
            else:
                server_default_value = column.server_default
                if isinstance(server_default_value, str):
                    server_default_clause = f" DEFAULT '{server_default_value}'"
                    default_value_for_update = f"'{server_default_value}'"
                else:
                    server_default_clause = f" DEFAULT {server_default_value}"
                    default_value_for_update = str(server_default_value)
        
        # Check for regular default if no server_default
        elif column.default is not None:
            if hasattr(column.default, 'arg'):
                # For callable defaults like func.now()
                if callable(column.default.arg):
                    if hasattr(column.default.arg, '__name__') and column.default.arg.__name__ == 'now':
                        default_clause = " DEFAULT NOW()"
                        default_value_for_update = "NOW()"
                    else:
                        # For other callables, try to get the value
                        try:
                            default_value = column.default.arg()
                            if isinstance(default_value, str):
                                default_clause = f" DEFAULT '{default_value}'"
                                default_value_for_update = f"'{default_value}'"
                            else:
                                default_clause = f" DEFAULT {default_value}"
                                default_value_for_update = str(default_value)
                        except Exception as e:
                            logger.warning(f"Could not evaluate callable default for column {column.name}: {e}")
                            default_clause = ""
                else:
                    # For literal defaults
                    default_value = column.default.arg
                    if isinstance(default_value, str):
                        default_clause = f" DEFAULT '{default_value}'"
                        default_value_for_update = f"'{default_value}'"
                    else:
                        default_clause = f" DEFAULT {default_value}"
                        default_value_for_update = str(default_value)
            else:
                # For simple defaults
                default_value = column.default
                if isinstance(default_value, str):
                    default_clause = f" DEFAULT '{default_value}'"
                    default_value_for_update = f"'{default_value}'"
                else:
                    default_clause = f" DEFAULT {default_value}"
                    default_value_for_update = str(default_value)
        
        # Handle nullable constraint
        nullable = "" if column.nullable else " NOT NULL"
        
        # If column is NOT NULL and has no default, we need to add it as nullable first
        if not column.nullable and not default_clause and not server_default_clause:
            logger.warning(f"Column {column.name} is NOT NULL but has no default. Adding as nullable first.")
            nullable = ""
        
        # Build the ALTER TABLE statement
        alter_stmt = f"ALTER TABLE {table_name} ADD COLUMN {column.name} {column_type}{nullable}{server_default_clause}{default_clause}"
        logger.info(f"Executing: {alter_stmt}")
        
        async with engine.begin() as conn:
            # Execute the ALTER TABLE statement
            await conn.execute(text(alter_stmt))
            logger.info(f"Successfully added column {column.name} to table {table_name}")
            
            # If column was added as nullable but should be NOT NULL, update rows and then alter
            if not column.nullable and (default_clause or server_default_clause or default_value_for_update):
                # Update existing rows with default value
                if default_value_for_update is not None:
                    logger.info(f"Updating existing rows with default value for column {column.name}")
                    
                    # Build the UPDATE statement
                    if default_value_for_update == "NOW()":
                        update_stmt = f"UPDATE {table_name} SET {column.name} = NOW() WHERE {column.name} IS NULL"
                    else:
                        update_stmt = f"UPDATE {table_name} SET {column.name} = {default_value_for_update} WHERE {column.name} IS NULL"
                    
                    logger.info(f"Executing UPDATE: {update_stmt}")
                    await conn.execute(text(update_stmt))
                    logger.info(f"Updated existing rows with default value for column {column.name}")
                
                # Now make the column NOT NULL
                alter_not_null_stmt = f"ALTER TABLE {table_name} ALTER COLUMN {column.name} SET NOT NULL"
                logger.info(f"Making column NOT NULL: {alter_not_null_stmt}")
                await conn.execute(text(alter_not_null_stmt))
                logger.info(f"Successfully made column {column.name} NOT NULL")
            elif default_value_for_update is not None:
                # Column already has default, just update existing rows
                logger.info(f"Updating existing rows with default value for column {column.name}")
                
                # Build the UPDATE statement
                if default_value_for_update == "NOW()":
                    update_stmt = f"UPDATE {table_name} SET {column.name} = NOW() WHERE {column.name} IS NULL"
                else:
                    update_stmt = f"UPDATE {table_name} SET {column.name} = {default_value_for_update} WHERE {column.name} IS NULL"
                
                logger.info(f"Executing UPDATE: {update_stmt}")
                await conn.execute(text(update_stmt))
                logger.info(f"Updated existing rows with default value for column {column.name}")
            else:
                logger.info(f"No default value to apply for column {column.name}")
    
    async def get_table_dependencies(table):
        """Get foreign key dependencies for a table"""
        dependencies = set()
        for column in table.columns:
            for fk in column.foreign_keys:
                # Extract table name from foreign key reference
                ref_table = fk.column.table.name
                if hasattr(fk.column.table, 'schema') and fk.column.table.schema:
                    ref_table = f"{fk.column.table.schema}.{ref_table}"
                dependencies.add(ref_table)
        return dependencies
    
    async def sort_tables_by_dependencies(model_tables):
        """Sort tables by their dependencies to ensure proper creation order"""
        logger.info("Sorting tables by dependencies...")
        
        # Create a dependency graph
        dependency_graph = {}
        for table_name, table in model_tables.items():
            dependencies = await get_table_dependencies(table)
            dependency_graph[table_name] = dependencies
            logger.info(f"Table {table_name} depends on: {list(dependencies)}")
        
        # Topological sort
        sorted_tables = []
        visited = set()
        temp_visited = set()
        
        def visit(table_name):
            if table_name in temp_visited:
                # Circular dependency detected
                logger.warning(f"Circular dependency detected for table: {table_name}")
                return
            if table_name in visited:
                return
            
            temp_visited.add(table_name)
            
            # Visit dependencies first
            for dep in dependency_graph.get(table_name, []):
                if dep in model_tables:  # Only visit tables we're creating
                    visit(dep)
            
            temp_visited.remove(table_name)
            visited.add(table_name)
            sorted_tables.append(table_name)
        
        # Visit all tables
        for table_name in model_tables:
            if table_name not in visited:
                visit(table_name)
        
        logger.info(f"Tables sorted by dependencies: {sorted_tables}")
        return sorted_tables
    
    async def compare_and_migrate_schema():
        """Main migration function"""
        logger.info("Starting schema migration process...")
        
        # Create engine
        engine = create_async_engine(database_url, echo=False)
        
        try:
            # Get model enums and tables
            model_enums = await get_model_enums()
            model_tables = await get_model_tables()
            
            # Get database enums and tables
            db_enums = await get_database_enums(engine)
            db_tables = await get_database_tables(engine)
            
            # Step 1: Create missing enum types first
            logger.info("Step 1: Creating missing enum types...")
            for enum_name, enum_data in model_enums.items():
                logger.info(f"Checking enum '{enum_name}' (used in {enum_data['table']}.{enum_data['column']})...")
                if enum_name not in db_enums:
                    logger.info(f"  Enum '{enum_name}' does not exist in database, creating...")
                    await create_enum(engine, enum_name, enum_data)
                else:
                    logger.info(f"  Enum '{enum_name}' already exists in database with values: {db_enums[enum_name]}")
                    # Check if values match
                    if set(enum_data['values']) != set(db_enums[enum_name]):
                        logger.warning(f"  WARNING: Enum '{enum_name}' values mismatch!")
                        logger.warning(f"    Model values: {enum_data['values']}")
                        logger.warning(f"    Database values: {db_enums[enum_name]}")
                    else:
                        logger.info(f"  Enum '{enum_name}' values match between model and database")
            
            # Step 2: Create missing tables in dependency order
            logger.info("Step 2: Creating missing tables...")
            sorted_table_names = await sort_tables_by_dependencies(model_tables)
            
            for table_name in sorted_table_names:
                table = model_tables[table_name]
                # Extract the actual table name without schema prefix for comparison
                actual_table_name = table_name.replace("public.", "")
                
                if table_name not in db_tables and actual_table_name not in db_tables:
                    logger.info(f"Table {table_name} does not exist in database, creating...")
                    await create_table(engine, table_name, table)
                else:
                    logger.info(f"Table {table_name} already exists in database")
            
            # Step 3: Check and add missing columns
            logger.info("Step 3: Checking for missing columns...")
            for table_name, table in model_tables.items():
                # Extract the actual table name without schema prefix for database operations
                actual_table_name = table_name.replace("public.", "")
                
                if table_name in db_tables or actual_table_name in db_tables:
                    logger.info(f"Checking columns for table: {table_name}")
                    
                    # Get database columns using the actual table name
                    db_columns = await get_table_columns(engine, actual_table_name)
                    
                    # Get model columns
                    model_columns = {col.name: col for col in table.columns}
                    
                    # Check for missing columns
                    for col_name, model_col in model_columns.items():
                        if col_name not in db_columns:
                            logger.info(f"Column {col_name} missing in table {table_name}, adding...")
                            await add_column(engine, actual_table_name, model_col, table)
                        else:
                            logger.info(f"Column {col_name} already exists in table {table_name}")
            
            logger.info("Schema migration completed successfully!")
            return True
            
        except Exception as e:
            logger.error(f"Error during schema migration: {e}")
            import traceback
            traceback.print_exc()
            return False
        finally:
            await engine.dispose()
    
    # Run the migration
    success = asyncio.run(compare_and_migrate_schema())
    
    if success:
        logger.info("Migration completed successfully!")
        sys.exit(0)
    else:
        logger.error("Migration failed!")
        sys.exit(1)
        
except Exception as e:
    logger.error(f"ERROR: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
EOF

    # Run the schema migration
    print_info "Running schema migration..."
    if python /tmp/schema_migration.py; then
        print_success "Schema migration completed successfully!"
        return 0
    else
        print_error "Schema migration failed!"
        return 1
    fi
}

# =============================================================================
# MAIN EXECUTION
# =============================================================================

# The first argument to this script determines the action: 'migrate' or 'start'.
# Defaults to 'start' if no command is provided
COMMAND="${1:-start}"

print_info "Entrypoint script started with command: $COMMAND"
print_info "Environment variables:"
print_info "  - DB_HOST: $DB_HOST"
print_info "  - DB_NAME: $DB_NAME"
print_info "  - DB_USER: $DB_USER"
print_info "  - DB_PASS: ${DB_PASS:0:3}***"
print_info "  - PYTHONPATH: $PYTHONPATH"
print_info "  - Current directory: $(pwd)"

case "$COMMAND" in
    "migrate")
        echo "📦 Waiting for PostgreSQL before running migrations..."
        print_info "Running wait_until_postgres.sh script..."
        
        if [ -f "/app/scripts/wait_until_postgres.sh" ]; then
            print_info "Found wait_until_postgres.sh at /app/scripts/wait_until_postgres.sh"
            /app/scripts/wait_until_postgres.sh
        else
            print_warning "wait_until_postgres.sh not found at /app/scripts/wait_until_postgres.sh"
            print_info "Checking for script in other locations..."
            find /app -name "wait_until_postgres.sh" 2>/dev/null || print_error "Script not found anywhere"
        fi
        
        print_info "Migration environment detected, running schema migration..."
        
        run_schema_migration
        ;;
    
    "start")
        echo "🚀 Waiting for PostgreSQL before starting the application..."
        print_info "Running wait_until_postgres.sh script..."
        
        if [ -f "/app/scripts/wait_until_postgres.sh" ]; then
            print_info "Found wait_until_postgres.sh at /app/scripts/wait_until_postgres.sh"
            /app/scripts/wait_until_postgres.sh
        else
            print_warning "wait_until_postgres.sh not found at /app/scripts/wait_until_postgres.sh"
            print_info "Checking for script in other locations..."
            find /app -name "wait_until_postgres.sh" 2>/dev/null || print_error "Script not found anywhere"
        fi
        
        print_info "Application startup environment detected, running migration check..."
        
        run_schema_migration
        
        echo "🚀 Starting backend with debugpy..."
        print_info "Starting uvicorn server on 0.0.0.0:8000 with debugpy on 0.0.0.0:5678"
        exec python -m debugpy \
            --listen 0.0.0.0:5678 \
            -m uvicorn main:app \
            --host 0.0.0.0 \
            --port 8000 \
            --log-level debug
        ;;
    
    *)
        # If an unknown command is given, execute it directly.
        # This allows for running other commands like 'bash'.
        print_info "Executing command: $*"
        exec "$@"
        ;;
esac

