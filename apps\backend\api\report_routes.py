import logging
import random
logger = logging.getLogger(__name__)
from fastapi import (APIR<PERSON><PERSON>, Request, Depends, WebSocket, WebSocketDisconnect, 
                     Query, HTTPException, Response)
from fastapi.encoders import jsonable_encoder
import sqlalchemy
from sqlalchemy.ext.asyncio import AsyncSession
import asyncio
from typing import Optional, List
import uuid
from datetime import datetime
import pytz

from database.db import get_db, get_database_async_engine

from services.report_service import UserReportsService
from services.minio_service import load_from_minio
from services.auth_service import auth_guard
from services.credits_service import CreditsService
from services.organization_users_service import OrganizationUsersService
from services.organization_service import OrganizationService
from services.user_service import UserStandaloneService
from services.apikey_service import ApikeyService
from services.invite_service import InviteService
from services.report_executions_service import ReportExecutionsService

from schemas.report_schema import SnapApiRequest, InsertReport, InsertV<PERSON>fier, ReportGetByFolder, RenameReport

from core.jwt_utils import logout_user_using_token
from core.constants import (<PERSON><PERSON><PERSON>Rep<PERSON><PERSON>, ReportStatus,
                            SummaryReportStatus, ReportMockValidator,DefaultPageLogs, Roles)

from models.enums import invite_status

from utils.jwt_utils import JWTUtils
from exceptions.business_exceptions import (
    InvitePermissionDeniedError,
    InvalidReportRequestFormatError,
    UserIdNotFoundTokenError,
    UserNotFoundError,
    FailToAccessDataToGetPendingReportsApiError,
    InputValueSnapWrongError,  # Added import
    SnapApiNoIdError,  # Added import
    ApiCreditsServiceUnavailableError,
    ApiCreditsFetchError
)
from exceptions.base_exceptions import InternalServerError  # Add this import if missing


router = APIRouter()


@router.websocket("/ws/snap-status/{user_id}")
async def websocket_endpoint(websocket: WebSocket, user_id: str):
    app = websocket.app
    manager = app.state.connection_manager

    logger.info(f"[websocket_endpoint][user({user_id})] WebSocket connection request received.")

    await manager.connect(websocket, user_id)
    logger.info(f"[websocket_endpoint][user({user_id})] WebSocket connection established and registered with manager.")
    

    try:
        while True:
            logger.info(f"[websocket_endpoint][user({user_id})] Starting new pending reports check loop.")
            
            db_gen = get_db()
            try:
                logger.debug(f"[websocket_endpoint][user({user_id})] Attempting to connect to DB for pending reports fetch.")
                db = await anext(db_gen)
                logger.info(f"[websocket_endpoint][user({user_id})] Connected to DB.")
            except Exception as e:
                logger.error(f"[get_pending_reports_by_user][user({user_id})] DB connection failed: {e}")
                raise FailToAccessDataToGetPendingReportsApiError()
            
            temp_service = UserReportsService(db=db, user_id=user_id)
            logger.debug(f"[websocket_endpoint][user({user_id})] Fetching pending reports from service.")
            pending_reports = await temp_service.get_pending_reports()
            pending_reports_no = len(pending_reports)
            logger.info(f"[websocket_endpoint][user({user_id})] Found {pending_reports_no} pending reports.")

            for report in pending_reports:
                report_id, report_type, report_status = report
                logger.info(f"[websocket_endpoint][user({user_id})] Processing pending report: id={report_id}, type={report_type}, status={report_status}")
                object_name = f"{user_id}_{report_id}.json"

                logger.debug(f"[websocket_endpoint][user({user_id})] Attempting to load report result from MinIO: object_name={object_name}")
                result = await load_from_minio(
                    bucket_name="processed-reports",
                    object_name=object_name,
                    user_id=user_id
                )

                if not result:
                    logger.warning(f"[websocket_endpoint][user({user_id})] Report {report_id} (type={report_type}) not found in MinIO bucket 'processed-reports' after polling.")
                    continue  # Optionally: send a status to the client about missing file

                logger.info(f"[websocket_endpoint][user({user_id})] Successfully loaded result from MinIO for report {report_id}.")
                message = {
                    "id": report_id,
                    "status_code": SummaryReportStatus.success,
                    "result": result,
                }
                safe_message = jsonable_encoder(message)

                try:
                    logger.debug(f"[websocket_endpoint][user({user_id})] Sending report result via WebSocket for report {report_id}.")
                    await websocket.send_json(safe_message)
                    logger.info(f"[websocket_endpoint][user({user_id})] Sent WebSocket report result for report {report_id}.")
                    report_executions_service = ReportExecutionsService(db=db, user_id=user_id)
                    await report_executions_service.update_column(
                        user_reports_id=report_id,
                        column_name="dt_envio_websocket",
                        value=datetime.now(pytz.timezone("UTC"))
                    )

                except Exception as send_err:
                    logger.warning(f"[websocket_endpoint][user({user_id})] Failed to send WebSocket message for report {report_id}: {send_err}")
                    continue

            logger.info(f"[websocket_endpoint][user({user_id})] Finished sending websocket results for {pending_reports_no} pending reports in this loop.")
            logger.debug(f"[websocket_endpoint][user({user_id})] Sleeping before next pending reports check.")
            sleep_time = random.randint(15, 30)
            await asyncio.sleep(sleep_time)
            
            logger.debug(f"[websocket_endpoint][user({user_id})] Checking for new pending reports after sleep.")
            pending_reports = await temp_service.get_pending_reports()
            pending_reports_no = len(pending_reports)
            
            if len(pending_reports) == 0:
                logger.info("[pending_reports_loop][user(%s)] No pending reports found.", user_id)
                break

    except WebSocketDisconnect:
        logger.info(f"[websocket_endpoint][user({user_id})] WebSocket disconnected for request.")
    finally:
        await manager.disconnect(user_id)
        logger.info(f"[websocket_endpoint][user({user_id})] Connection closed and cleaned up.")


@router.put("/insert-report/{user_reports_id}")
async def insert_report(request: InsertReport, user_reports_id: str, db: AsyncSession = Depends(get_db), user=Depends(auth_guard)):
    request_keys = list(request.model_dump().keys())

    token_decoded = JWTUtils(user_jwt=user)
    user_id = token_decoded.get_user_id()
    user_roles = token_decoded.get_user_roles()
    logger.info(f"[insert-report] Decoded user_id: {user_id}, user_roles: {user_roles}")

    if Roles.update_report not in user_roles:
        logger.warning("[insert-report] Access denied - User %s does not have update report role", user_id)
        raise InvitePermissionDeniedError("atualizar relatório")

    # Create service with user_reports_id
    user_reports_service = UserReportsService(db=db, user_id=user_id, user_reports_id=user_reports_id)
    
    # Use the service to handle the report insertion
    await user_reports_service.populate_report_handler(body=request)
    
    return {"message": "Report updated successfully"}


@router.get("/get-one-report/{user_reports_id}")
async def get_one_report(user_reports_id: str, db: AsyncSession = Depends(get_db), user=Depends(auth_guard)):
    logger.info("[get_one_report][user(%s)] Fetching report %s.", user, user_reports_id)

    token_decoded = JWTUtils(user_jwt=user)

    user_id = token_decoded.get_user_id()
    user_roles = token_decoded.get_user_roles()
    logger.info(f"[get-one-report] Decoded user_id: {user_id}, user_roles: {user_roles}")

    if Roles.view_report_details not in user_roles:
        logger.warning("[get-one-report] Access denied - User %s does not have view report details role", user_id)
        raise InvitePermissionDeniedError("visualizar detalhes do relatório")
    
    user_reports_service = UserReportsService(db=db, user_id=user_id, user_reports_id=user_reports_id)
    return await user_reports_service.get_one_report_handler()


@router.get("/get-logs")
async def get_logs_endpoint(limit: int = Query(DefaultPageLogs.pagedefault),
    page: int = Query(1),
    order: str = Query("desc", description="Sort direction: 'asc' or 'desc'"),
    column_order: str = Query("created_at", description="Column to order by"),
    is_user: Optional[bool] = Query(True, description="Column to choose if is user or org filter"),
    report_type: Optional[str] = Query(None, description="Type of report"),
    created_start_at: Optional[datetime] = Query(None, description="Intial date to filter"),
    created_end_at: Optional[datetime] = Query(None, description="Final date to filter"),
    db: AsyncSession = Depends(get_db),
    user: dict = Depends(auth_guard)):

    logger.info(f"[get_logs_endpoint] Called with params: limit={limit}, page={page}, order={order}, column_order={column_order}, is_user={is_user}, report_type={report_type}, created_start_at={created_start_at}, created_end_at={created_end_at}")
    logger.info(f"[get_logs_endpoint] Authenticated user: {user}")
    token_decoded = JWTUtils(user_jwt=user)

    user_id = token_decoded.get_user_id()
    organization_id= None
    user_roles = token_decoded.get_user_roles()
    logger.info(f"[get_logs_endpoint] Decoded user_id: {user_id}, user_roles: {user_roles}")

    if Roles.get_user_logs not in user_roles:
        logger.warning("[get_logs_endpoint] Access denied - User %s does not have get user logs role", user_id)
        raise InvitePermissionDeniedError("obter logs do usuário")

    if Roles.get_organization_logs in user_roles:
        logger.info(f"[get_logs_endpoint] User has organization logs role. Fetching organization id for user {user_id}")
        is_user=False
        organization_user_service = OrganizationUsersService(db=db, user_id=user_id)
        organization_user_data = await organization_user_service.get_organization_user()
        logger.info(f"[get_logs_endpoint] organization_user_data: {organization_user_data}")
        organization_id = organization_user_data.organization_id
        logger.info(f"[get_logs_endpoint] Updated id to organization_id: {organization_id}")

    user_reports_service = UserReportsService(db=db, user_id=user_id, organization_id=organization_id)
    logger.info(f"[get_logs_endpoint] Created UserReportsService for user_id: {user_id}")

    result = await user_reports_service.get_all_logs(limit=limit, page=page, order=order, column_order=column_order,
                                               is_user=is_user, report_type=report_type, 
                                               created_start_at=created_start_at, created_end_at=created_end_at)
    logger.info(f"[get_logs_endpoint] get_all_logs result: {result}")

    return result


@router.post("/get-data-from-snap-api")
async def get_data_from_snap_api(
        request: Request,
        user: dict = Depends(auth_guard),
        db: AsyncSession = Depends(get_db)):
    

    token_decoded = JWTUtils(user_jwt=user)

    user_id = token_decoded.get_user_id()
    user_roles = token_decoded.get_user_roles()
    logger.info(f"[get_data_from_snap_api] Decoded user_id: {user_id}, user_roles: {user_roles}")

    if Roles.create_report not in user_roles:
        logger.warning("[get_data_from_snap_api] Access denied - User %s does not have create report role", user_id)
        raise InvitePermissionDeniedError("criar relatório")

    organization_user_services = OrganizationUsersService(db=db, user_id=user_id)
    org_data = await organization_user_services.get_organization_user()
    organization_id = None
    if org_data:
        organization_id = org_data.organization_id
        logger.info(f"[get_data_from_snap_api] Active organization found for user: {user_id}")


    # Log request information with proper redaction
    raw_body = await request.body()
    safe_body = f"Length: {len(raw_body)} bytes"  # Avoid logging potentially sensitive data
    logger.info(f"[get_data_from_snap_api][user({user_id})] Request received: {safe_body}")

    # Parse request body with proper error handling
    try:
        parsed_body = SnapApiRequest(**await request.json())
        logger.info(f"[get_data_from_snap_api][user({user_id})] Processing request type: {parsed_body.report_type}")
    except ValueError as e:
        logger.error(f"[get_data_from_snap_api][user({user_id})] Invalid request format: {str(e)}")
        raise InvalidReportRequestFormatError(reason=str(e))
    except Exception as e:
        logger.error(f"[get_data_from_snap_api][user({user_id})] Failed to parse request: {str(e)}")
        raise InvalidReportRequestFormatError(reason=f"Falha ao processar requisição: {str(e)}")

    # Create report status information
    report_status = {"status_report": ReportStatus.InProgress.pending_low_case}
    if parsed_body.report_type=="relacoes":
        report_search_args = parsed_body.report_input_value
    else:
        report_search_args = {parsed_body.report_type: parsed_body.report_input_value}

    user_reports_id = parsed_body.user_reports_id
    client_id = None
    app = request.app
    # Deduct quota for the API call

    user_reports_service=UserReportsService(db=db, user_id=user_id, user_reports_id=user_reports_id, organization_id=organization_id)
    user_reports_service.set_user_data(user_data=user)

    api_key = await user_reports_service.validate_access_to_report(report_type=parsed_body.report_type)

    # Process the request with retry logic
    max_retries = 3
    for attempt in range(1, max_retries + 1):
        try:
            # Handle test cases differently from real API calls
            if ReportMockValidator.is_test_case(parsed_body.report_type, parsed_body.report_input_value):
                client_id = str(uuid.uuid4())
                client = {'id': client_id}
                logger.info(
                    f"[get_data_from_snap_api][user({user_id})] Processing test case, generated client_id: {client_id}")
            else:
                client = await user_reports_service.get_data_from_snap_api_handler(body=parsed_body)
                client_id = client.get('id')
                if not client_id:
                    raise SnapApiNoIdError()

            # Update report status with client ID
            report_status["snap_request_id"] = client_id


            # Create or update report based on whether user_reports_id was provided
            if user_reports_id is None:
                logger.info(f"[get_data_from_snap_api][user({user_id})] Creating new report for client {client_id}")
                user_reports_id = await user_reports_service.create_blank_report_with_status(
                    status_report=report_status,
                    report_type=parsed_body.report_type,
                    report_input_encrypted=parsed_body.report_input_encrypted,
                    folder_id=parsed_body.parent_folder_id
                )
            else:
                logger.info(f"[get_data_from_snap_api][user({user_id})] Updating existing report {user_reports_id}")
                await user_reports_service.update_error_report_to_pending(
                    status_report=report_status
                )

            # Get report metrics
            report_number = await user_reports_service.get_number_of_report_type(
                report_type=parsed_body.report_type
            )
            logger.info(f"[get_data_from_snap_api][user({user_id})] Report number: {report_number}")

            # Create unique task key and check if already running
            task_key = (user_id, user_reports_id)
            if task_key in app.state.running_snap_tasks:
                logger.info(f"[get_data_from_snap_api][user({user_id})] Task already running for {user_reports_id}")
                return client

            # Create and configure background task
            task = asyncio.create_task(
                user_reports_service.snap_status_ws(
                    app=app,
                    request_id=client_id,
                    report_type=parsed_body.report_type,
                    report_number=report_number,
                    report_search_args=report_search_args,
                    api_key=api_key
                )
            )

            # Add cleanup callback and store task reference
            task.add_done_callback(lambda _: app.state.running_snap_tasks.pop(task_key, None))
            app.state.running_snap_tasks[task_key] = task

            return client

        except HTTPException as http_ex:
            if http_ex.status_code == 422:
                # Don't retry validation errors
                logger.error(f"[get_data_from_snap_api][user({user_id})] Invalid input format: {http_ex.detail}")
                raise InputValueSnapWrongError()  # Use business exception

            # Log other HTTP exceptions and potentially retry
            logger.warning(
                f"[get_data_from_snap_api][user({user_id})] Attempt {attempt} failed with HTTP status {http_ex.status_code}: {http_ex.detail}"
            )

            if attempt >= max_retries:
                await user_reports_service.handle_max_retries_exceeded(
                    app, client_id, user_reports_id, parsed_body, http_ex
                )
                credits_service = CreditsService(db=db, user_id=user_id)
                await credits_service.change_user_credits(credit_delta= 1)
                break

        except Exception as e:
            # Log general exceptions and potentially retry
            logger.warning(
                f"[get_data_from_snap_api][user({user_id})] Attempt {attempt} failed: {str(e)}"
            )

            if attempt >= max_retries:
                await user_reports_service.handle_max_retries_exceeded(
                    app, client_id, user_reports_id, parsed_body, e
                )
                credits_service = CreditsService(db=db, user_id=user_id)
                await credits_service.change_user_credits(credit_delta= 1)
                break

        # Wait before retry
        if attempt < max_retries:
            await asyncio.sleep(1 * attempt)  # Exponential backoff


@router.get("/get-saved-reports")
async def get_saved_reports(
    folder_id: Optional[str] = Query(None, description="Optional folder ID"),
    limit: int = Query(DefaultReports.DEFAULT_REPORTS_TO_FETCH),
    page: int = Query(1),
    order: str = Query("desc", description="Sort direction: 'asc' or 'desc'"),
    column_order: str = Query("modified_at", description="Column to order by"),
    hmac_filter: Optional[List[str]] = Query(None, description="Optional HMAC filter"),
    hmac_column: Optional[str] = Query(None, description="Column to filter hmac"),
    db: AsyncSession = Depends(get_db),
    user: dict = Depends(auth_guard)
):
    logger.info("[get_saved_reports][user(%s)] Fetching saved reports.", user)

    token_decoded = JWTUtils(user_jwt=user)

    user_id = token_decoded.get_user_id()
    user_roles = token_decoded.get_user_roles()
    logger.info(f"[get_saved_reports] Decoded user_id: {user_id}, user_roles: {user_roles}")

    if Roles.view_report_list not in user_roles:
        logger.warning("[get_saved_reports] Access denied - User %s does not have view report list role", user_id)
        raise InvitePermissionDeniedError("visualizar lista de relatórios")
    
    user_reports = UserReportsService(db=db,user_id=user_id)
    user_reports.set_user_data(user_data=user)
    saved_reports = await user_reports.get_saved_reports_handler(
                limit=limit,
                page=page,
                order=order,
                column_order=column_order, 
                hmac_filter=hmac_filter,
                hmac_column=hmac_column,
                folder_id=folder_id,
                get_data_child_folders=True)

    return saved_reports


@router.get("/get-report-by-folder")
async def get_report_by_folder(folder_id: Optional[str] = Query(None, description="Optional folder ID"),
                                limit: int = Query(DefaultPageLogs.pagedefault),
                                page: int = Query(1),
                                order: str = Query("desc", description="Sort direction: 'asc' or 'desc'"),
                                column_order: str = Query("modified_at", description="Column to order by"),
                                hmac_filter: Optional[List[str]] = Query(None, description="Optional HMAC filter"),
                                hmac_column: Optional[str] = Query(None, description="Column to filter hmac"),
                                db: AsyncSession = Depends(get_db), user: dict = Depends(auth_guard)):


    logger.info("[get_report_by_folder] Fetching report by folder %s.", folder_id)
    token_decoded = JWTUtils(user_jwt=user)
    user_id = token_decoded.get_user_id()
    user_roles = token_decoded.get_user_roles()
    logger.info(f"[get_report_by_folder] Decoded user_id: {user_id}, user_roles: {user_roles}")

    if Roles.view_report_list not in user_roles:
        logger.warning("[get_report_by_folder] Access denied - User %s does not have view report list role", user_id)
        raise InvitePermissionDeniedError("visualizar lista de relatórios")

    user_reports = UserReportsService(db=db,user_id=user_id)
    user_reports.set_user_data(user_data=user)
    saved_reports = await user_reports.get_reports_by_folder(
                limit=limit,
                page=page,
                order=order,
                column_order=column_order, 
                hmac_filter=hmac_filter,
                hmac_column=hmac_column,
                folder_id=folder_id)

    return saved_reports


@router.get("/auth/user")
async def get_authenticated_user(
    db: AsyncSession = Depends(get_db),
    user: dict = Depends(auth_guard)
):
    logger.info("[get_authenticated_user] Called endpoint.")

    token_decoded = JWTUtils(user_jwt=user)

    user_id = token_decoded.get_user_id()
    user_roles = token_decoded.get_user_roles()
    user_email = token_decoded.get_user_email()
    logger.info(f"[get_authenticated_user] Decoded user_id: {user_id}, user_roles: {user_roles}")


    logger.info(f"[get_authenticated_user][user({user_id})] Extracted user_id from token: {user_id}")
    if not user_id:
        logger.error("[get_authenticated_user][user(%s)] No user ID found in token.", user_id)
        raise UserIdNotFoundTokenError()

    logger.info("[get_authenticated_user][user(%s)] Fetching user data from DB.", user_id)

    engine = get_database_async_engine()
    query = """
        SELECT * FROM public.users
        WHERE user_id = :user_id;
    """

    async with engine.connect() as conn:
        logger.info(f"[get_authenticated_user][user({user_id})] Executing user query: {query} with user_id={user_id}")
        result = await conn.execute(sqlalchemy.text(query), {"user_id": user_id})
        row = result.fetchone()
        logger.info(f"[get_authenticated_user][user({user_id})] Query result: {row}")

    if row:
        logger.info("[get_authenticated_user][user(%s)] User found.", user_id)
        
        # Converta para dicionário desde o início
        user_data = dict(row._mapping)

        logger.info(f"[get_authenticated_user][user({user_id})] User roles from JWT: {user_roles}")
        user_data["roles"] = user_roles

        if isinstance(user_data["report_types"], dict) and not user_data["report_types"]:
            logger.info(f"[get_authenticated_user][user({user_id})] report_types is an empty dict, converting to empty list.")
            user_data["report_types"] = []
            logger.info(f"[get_authenticated_user][user({user_id})] report_types after conversion: {user_data['report_types']}")

        organization_user_service = OrganizationUsersService(db=db, user_id=user_id)
        logger.info(f"[get_authenticated_user][user({user_id})] Fetching organization user info.")
        organization_user = await organization_user_service.get_organization_user()
        logger.info(f"[get_authenticated_user][user({user_id})] organization_user value: {organization_user}, type: {type(organization_user)}")
        organization_id = None
        user_data["organization_name"] = None
        user_data["organization_logo"] = None
        user_data["print_snap_logo"] = False
        if organization_user is not None:
            organization_id = organization_user.organization_id
            logger.info(f"[get_authenticated_user][user({user_id})] User belongs to organization: {organization_id}")

            organization_service = OrganizationService(db=db, organization_id=organization_id)
            organization_data = await organization_service.get_organization_data()

            if organization_data.name:
                user_data["organization_name"] = organization_data.name
                logger.info(f"[get_authenticated_user][user({user_id})] Organization name: {organization_data.name}")

            if organization_data.image_logo:
                user_data["organization_logo"] = organization_data.image_logo
                logger.info(f"[get_authenticated_user][user({user_id})] Organization logo: {organization_data.image_logo}")

            if organization_data.api_key and Roles.add_api_key in user_roles:
                user_data["api_key"] = organization_data.api_key
                logger.info(f"[get_authenticated_user][user({user_id})] Organization api_key: {organization_data.api_key}")
            
            if Roles.print_snap_logo in user_roles:
                user_data["print_snap_logo"] = organization_data.print_snap_logo
                logger.info(f"[get_authenticated_user][user({user_id})] Organization print snap logo: {organization_data.print_snap_logo}")
                
        else:
            logger.info(f"[get_authenticated_user][user({user_id})] User does not belong to any organization.")

        year = datetime.today().year
        month = datetime.today().month
        logger.info(f"[get_authenticated_user][user({user_id})] Current year: {year}, month: {month}")

        user_standalone_service = UserStandaloneService(db=db, user_id=user_id)
        if organization_id is not None:
            user_standalone_service.set_organization_id(organization_id=organization_id)
        report_count = await user_standalone_service.count_reports_by_month_year(month=month, year=year)
        logger.info(f"[get_authenticated_user][user({user_id})] Report count: {report_count}")
        if report_count:
            user_data["report_count"] = report_count

        api_key_service = ApikeyService(db=db, user_id=user_id)
        api_key = await api_key_service.get_api_key()
        logger.info(f"[get_authenticated_user][user({user_id})] API key: {api_key}")
        api_credits=0
        api_next_reset_credits=None
        user_data["api_next_reset_credits"] = None
        user_data["credits_minimun"] = None

        if api_key is not None:
            credits_service = CreditsService(db=db, user_id=user_id)
            credits_service.set_api_key(api_key=api_key)
            
            try:
                logger.info(f"[get_authenticated_user][user({user_id})] Attempting to fetch API credits from external service...")
                api_credits, api_next_reset_credits = await credits_service.get_api_credits()
                user_data["api_next_reset_credits"] = api_next_reset_credits
                logger.info(f"[get_authenticated_user][user({user_id})] Successfully fetched API credits: {api_credits}, next reset: {api_next_reset_credits}")

                try:
                    minimun_credits, _  = await credits_service.compare_user_credits_with_api_credits(user_data)
                    user_data["credits_minimun"] = minimun_credits
                    logger.info(f"[get_authenticated_user][user({user_id})] Minimum credits: {minimun_credits}")
                except ApiCreditsServiceUnavailableError as e:
                    logger.warning(f"[get_authenticated_user][user({user_id})] Caught ApiCreditsServiceUnavailableError during credits comparison: {e.detail}. Setting minimum credits to 0.")
                    user_data["credits_minimun"] = 0
                    # user_data["license_expired"] = True
                    # user_data["license_expired_message"] = e.detail
                    logger.info(f"[get_authenticated_user][user({user_id})] Successfully handled API credits service unavailability during comparison. Minimum credits set to 0.")
                except HTTPException as e:
                    # For any other HTTP error, let it fail the request
                    logger.error(f"[get_authenticated_user][user({user_id})] Unexpected HTTP error during credits comparison (status {e.status_code}): {e.detail}")
                    raise InternalServerError(detail=str(e))
                        
            except ApiCreditsServiceUnavailableError as e:
                logger.warning(f"[get_authenticated_user][user({user_id})] Caught ApiCreditsServiceUnavailableError: {e.detail}. Setting default values for user.")
                user_data["api_next_reset_credits"] = None
                user_data["credits_minimun"] = 0
                logger.info(f"[get_authenticated_user][user({user_id})] Successfully handled API credits service unavailability. User data updated with default values: api_next_reset_credits=None, credits_minimun=0")
            except ApiCreditsFetchError as e:
                logger.warning(f"[get_authenticated_user][user({user_id})] Caught ApiCreditsFetchError: {e.detail}. Setting default values for user.")
                user_data["api_next_reset_credits"] = None
                user_data["credits_minimun"] = 0
                logger.info(f"[get_authenticated_user][user({user_id})] Successfully handled API credits fetch error. User data updated with default values: api_next_reset_credits=None, credits_minimun=0")
            
            except HTTPException as e:
                # For any other HTTP error, let it fail the request
                logger.error(f"[get_authenticated_user][user({user_id})] Unexpected HTTP error from API credits service (status {e.status_code}): {e.detail}")
                user_data["api_next_reset_credits"] = None
                user_data["credits_minimun"] = 0
                logger.info(f"[get_authenticated_user][user({user_id})] Successfully handled API credits fetch error. User data updated with default values: api_next_reset_credits=None, credits_minimun=0")

        user_data["has_active_invite"]=False

        invite_service = InviteService(db=db, user_id=user_id)
        number_of_active_invite = await invite_service.get_user_invite(email=user_email,
                                                                 status_invite=invite_status.enviado)
        
        if len(number_of_active_invite)>0:
            user_data["has_active_invite"]=True


        logger.info(f"[get_authenticated_user][user({user_id})] Returning user data: {user_data}")
        return user_data
    
    else:
        
        logger.error("[get_authenticated_user][user(%s)] User not found.", user_id)
        raise UserNotFoundError(user_id)


@router.post("/verifier")
async def insert_verifier(request: InsertVerifier, db: AsyncSession = Depends(get_db), user: dict = Depends(auth_guard)):
    logger.info("[insert_verifier] Inserting verifier for user %s.", user.get('sub'))
    logger.info("[insert_verifier] Received request: %s", request.model_dump())

    token_decoded = JWTUtils(user_jwt=user)
    user_id = token_decoded.get_user_id()

    # Create service without user_reports_id (not needed for this operation)
    user_service = UserStandaloneService(db=db, user_id=user_id)
    
    # Use the service to handle the verifier insertion
    return await user_service.insert_verifier_handler(body=request)


@router.patch("/rename-report/{user_reports_id}")
async def rename_report(user_reports_id: str, request: RenameReport, db: AsyncSession = Depends(get_db), user: dict = Depends(auth_guard)):
    logger.info("[rename_report] Renaming report %s.", user_reports_id)
    token_decoded = JWTUtils(user_jwt=user)
    user_id = token_decoded.get_user_id()
    user_roles = token_decoded.get_user_roles()
    logger.info(f"[rename_report] Decoded user_id: {user_id}, user_roles: {user_roles}")

    if Roles.rename_report not in user_roles:
        logger.warning("[rename_report] Access denied - User %s does not have rename report role", user_id)
        raise InvitePermissionDeniedError("renomear relatório")

    user_reports_service = UserReportsService(db=db, user_id=user_id, user_reports_id=user_reports_id)
    await user_reports_service.rename_report_handler(body=request)
    return {"message": "Relatório renomeado com sucesso"}


@router.post("/logout")
async def logout_user(request: Request, response: Response):
    body = await request.body()
    logger.info("[logout_user] Received request: %s", body.decode("utf-8"))
    refresh_token = request.cookies.get("refresh_token")
    logger.info("[logout_user] Logging out user.")
    return await logout_user_using_token(refresh_token=refresh_token, response=response)


@router.delete("/delete-report/{user_reports_id}")
async def delete_report(user_reports_id: str, db: AsyncSession = Depends(get_db), user: dict = Depends(auth_guard)):
    logger.info("[delete_report] Deleting report %s.", user_reports_id)

    token_decoded = JWTUtils(user_jwt=user)
    user_id = token_decoded.get_user_id()
    user_roles = token_decoded.get_user_roles()
    logger.info(f"[delete_report] Decoded user_id: {user_id}, user_roles: {user_roles}")  
      
    if Roles.delete_report not in user_roles:
        logger.warning("[delete_report] Access denied - User %s does not have delete report role", user_id)
        raise InvitePermissionDeniedError("deletar relatório")

    user_reports_service = UserReportsService(db=db, user_id=user_id, user_reports_id=user_reports_id)
    return await user_reports_service.delete_report_handler()