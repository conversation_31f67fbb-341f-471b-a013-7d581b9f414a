import React, { useMemo } from "react";
import { ArrayRenderStrategy } from "./RenderStrategy";
import { Sociedade } from "../../model/Sociedades";
import { CustomGridContainer } from "../components/CustomGridContainer";
import { CustomGridItem } from "../components/CustomGridItem";
import { parseValue, translatePropToLabel } from "../../helpers";
import { useReportActions, useReportMode, useReportSections } from "../../context/ReportContext";
import { MdOutlineSubdirectoryArrowRight } from "react-icons/md";
import { CustomReadOnlyInputField } from "../components/CustomReadOnlyInput";
import { renderSourceTooltip } from "./helpers.strategy";
import { ReportsCustomLabel } from "../components/ReportsCustomLabel";
import { ItemSeparator } from "../components/ItemSeparator";

export function useRenderSociedades(
  sectionTitle: string
): ArrayRenderStrategy<Sociedade> {
  const actions = useReportActions();
  const mode = useReportMode();
  const isTrash = mode === "trash";

  // pull our section once, then memoize an entry→index map:
  const section = useReportSections().find((s) => s.title === sectionTitle);
  const idxMap = useMemo(() => {
    const m = new WeakMap<Sociedade, number>();
    section?.data.forEach((e, i) => m.set(e as any, i));
    return m;
  }, [section]);

  // include‐flag helper
  const includeKey = (isDeleted: boolean) =>
    isTrash ? isDeleted : !isDeleted;

  // Test functions for individual keys
  const testRazaoSocialDeleted = (e: any) => !e.razao_social || e.razao_social?.is_deleted === true;
  const testDetalhesDeleted = (e: any) =>
    e.detalhes
      ? Object.values(e.detalhes).every((v: any) => v.is_deleted === true)
      : false;
  const testEmailsDeleted = (e: any) =>
    e.emails
      ? e.emails.every((email: any) =>
        Object.values(email.value || {}).every((v: any) => v.is_deleted === true)
      )
      : true;
  const testTelefonesDeleted = (e: any) =>
    e.telefones
      ? e.telefones.every((t: any) =>
        Object.values(t.value || {}).every((v: any) => v.is_deleted === true)
      )
      : true;
  const testEnderecosDeleted = (e: any) =>
    e.enderecos
      ? e.enderecos.every((end: any) =>
        Object.values(end.value || {}).every((v: any) => v.is_deleted === true)
      )
      : true;

  const testGenericListsDeleted = (e: any) => {
    const genericListKeys = Object.keys(e).filter(key =>
      key !== 'razao_social' &&
      key !== 'detalhes' &&
      key !== 'telefones' &&
      key !== 'emails' &&
      key !== 'enderecos' &&
      Array.isArray(e[key])
    );

    if (genericListKeys.length === 0) return true;

    return genericListKeys.every(key => {
      const list = e[key] as any[];
      return list.every((item: any) => {
        if (item.value && typeof item.value === 'object') {
          return Object.values(item.value).every((field: any) => field.is_deleted === true);
        }
        return item.is_deleted === true;
      });
    });
  };

  const testEntryDeleted = (entry: any): boolean => {
    const hasRazaoSocial = !!entry.razao_social;
    const isRazaoSocialDeleted = hasRazaoSocial ? testRazaoSocialDeleted(entry) : true;
    const areDetalhesDeleted = testDetalhesDeleted(entry);
    const areEmailsDeleted = testEmailsDeleted(entry);
    const areTelefonesDeleted = testTelefonesDeleted(entry);
    const areEnderecosDeleted = testEnderecosDeleted(entry);
    const areGenericListsDeleted = testGenericListsDeleted(entry);

    // If there's no razao_social, only check other fields
    if (!hasRazaoSocial) {
      return areDetalhesDeleted && areEmailsDeleted && areTelefonesDeleted && areEnderecosDeleted && areGenericListsDeleted;
    }
    
    return isRazaoSocialDeleted && areDetalhesDeleted && areEmailsDeleted && areTelefonesDeleted && areEnderecosDeleted && areGenericListsDeleted;
  };

  const testSectionDeleted = (section: any): boolean =>
    Array.isArray(section.data) && section.data.every(testEntryDeleted);

  // Data count calculation function for Sociedades section
  const calculateDataCount = (section: any): number => {
    if (!Array.isArray(section.data)) return 0;

    return section.data.reduce((count: number, entry: any) => {
      // Count non-deleted entries (whole objects)
      // An entry is considered deleted only when ALL its parts are deleted
      const isEntryDeleted = testEntryDeleted(entry);
      return isEntryDeleted ? count : count + 1;
    }, 0);
  };

  // Helper functions for nested arrays
  const shouldIncludeNestedBlock = (item: any) => {
    const vals = Object.values(item.value || {});
    return isTrash
      ? vals.some((v: any) => v.is_deleted === true)
      : vals.some((v: any) => v.is_deleted === false);
  };

  const shouldIncludeList = (arrayItems: any[]) => {
    if (!Array.isArray(arrayItems)) return false;
    return arrayItems.some((item: any) => shouldIncludeNestedBlock(item));
  };

  const onToggleNestedField = (entryIdx: number, arrayKey: string, blockIdx: number, fieldKey: string) => {
    actions.updateSectionEntries!(
      sectionTitle,
      (e: any, i?: number) => {
        if (i === entryIdx) {
          const item = e[arrayKey]?.[blockIdx];
          if (item?.value?.[fieldKey]) {
            item.value[fieldKey].is_deleted = !item.value[fieldKey].is_deleted;

            // Verifica se todos os campos do bloco estão deletados
            const allFieldsDeleted = Object.values(item.value).every((campo: any) =>
              campo.is_deleted === true
            );

            // Se todos os campos estão deletados, marca o bloco como deletado
            item.is_deleted = allFieldsDeleted;
          }
        }
      },
      testEntryDeleted,
      testSectionDeleted,
      calculateDataCount
    );
  };

  const onToggleNestedBlock = (entryIdx: number, arrayKey: string, blockIdx: number) => {
    actions.updateSectionEntries!(
      sectionTitle,
      (e: any, i?: number) => {
        if (i === entryIdx) {
          const item = e[arrayKey]?.[blockIdx];
          if (item?.value) {
            // Determina o novo estado baseado no modo atual
            const targetDeletedState = isTrash ? false : true;

            // Define o is_deleted do bloco principal
            item.is_deleted = targetDeletedState;

            // Define o is_deleted de todos os campos dentro do bloco
            Object.values(item.value).forEach((campo: any) => {
              if (campo) {
                campo.is_deleted = targetDeletedState;
              }
            });
          }
        }
      },
      testEntryDeleted,
      testSectionDeleted,
      calculateDataCount
    );
  };

  const onToggleListTitle = (entryIdx: number, arrayKey: string) => {
    actions.updateSectionEntries!(
      sectionTitle,
      (e: any, i?: number) => {
        if (i === entryIdx) {
          const arrayItems = e[arrayKey];
          if (arrayItems && Array.isArray(arrayItems)) {
            const targetDeletedState = isTrash ? false : true;

            arrayItems.forEach((item: any) => {
              item.is_deleted = targetDeletedState;
              if (item.value) {
                Object.values(item.value).forEach((campo: any) => {
                  if (campo) {
                    campo.is_deleted = targetDeletedState;
                  }
                });
              }
            });
          }
        }
      },
      testEntryDeleted,
      testSectionDeleted,
      calculateDataCount
    );
  };

  const onToggleGenericListTitle = (idx: number, listKey: string) => {
    const updater = actions.updateSectionEntries;
    if (!updater) return;

    updater(
      sectionTitle,
      (entry, entryIndex) => {
        if (entryIndex === idx) {
          const list = entry[listKey] as any[];
          if (Array.isArray(list)) {
            const targetDeletedState = isTrash ? false : true;

            list.forEach((item: any) => {
              item.is_deleted = targetDeletedState;
              if (item.value && typeof item.value === 'object') {
                Object.values(item.value).forEach((field: any) => {
                  if (field && typeof field === 'object' && 'is_deleted' in field) {
                    field.is_deleted = targetDeletedState;
                  }
                });
              }
            });
          }
        }
      },
      testEntryDeleted,
      testSectionDeleted,
      calculateDataCount
    );
  };

  const onToggleGenericListItem = (idx: number, listKey: string, itemIdx: number) => {
    const updater = actions.updateSectionEntries;
    if (!updater) return;

    updater(
      sectionTitle,
      (entry, entryIndex) => {
        if (entryIndex === idx) {
          const item = entry[listKey]?.[itemIdx];
          if (item) {
            if (item.value && typeof item.value === 'object') {
              const targetDeletedState = isTrash ? false : true;

              item.is_deleted = targetDeletedState;

              Object.values(item.value).forEach((field: any) => {
                if (field && typeof field === 'object' && 'is_deleted' in field) {
                  field.is_deleted = targetDeletedState;
                }
              });
            } else {
              item.is_deleted = !item.is_deleted;
            }
          }
        }
      },
      testEntryDeleted,
      testSectionDeleted,
      calculateDataCount
    );
  };

  const onToggleGenericListField = (idx: number, listKey: string, itemIdx: number, fieldKey: string) => {
    const updater = actions.updateSectionEntries;
    if (!updater) return;

    updater(
      sectionTitle,
      (entry, entryIndex) => {
        if (entryIndex === idx) {
          const field = entry[listKey]?.[itemIdx]?.value?.[fieldKey];
          if (field) {
            field.is_deleted = !field.is_deleted;

            const item = entry[listKey]?.[itemIdx];
            if (item?.value) {
              const allFieldsDeleted = Object.values(item.value).every((f: any) => f.is_deleted === true);
              item.is_deleted = allFieldsDeleted;
            }
          }
        }
      },
      testEntryDeleted,
      testSectionDeleted,
      calculateDataCount
    );
  };

  const formatByKey: Record<
    string,
    (entry?: Sociedade) => React.ReactElement | null
  > = {
    razao_social: (entry) => {
      if (!entry?.razao_social || !includeKey(entry.razao_social.is_deleted || false)) return null;
      const idx = idxMap.get(entry)!;

      return (
        <CustomGridContainer cols={1} key={`razao_social-${idx}`} >
          <CustomGridItem
            fullWidth
            className="mb-6"
            onToggleField={() =>
              actions.updateSectionEntries!(
                sectionTitle,
                (e: any, i?: number) => {
                  if (i === idx && e.razao_social) {
                    e.razao_social.is_deleted = !e.razao_social.is_deleted;

                    // Cascading deletion logic: when razao_social is deleted, delete all other fields
                    // When razao_social is restored, restore all other fields
                    const targetDeletedState = e.razao_social.is_deleted;

                    // Apply to all detalhes
                    if (e.detalhes) {
                      Object.values(e.detalhes).forEach((detalhe: any) => {
                        if (detalhe) detalhe.is_deleted = targetDeletedState;
                      });
                    }

                    // Apply to emails array
                    if (e.emails) {
                      e.emails.forEach((email: any) => {
                        email.is_deleted = targetDeletedState;
                        if (email.value) {
                          Object.values(email.value).forEach((campo: any) => {
                            if (campo) campo.is_deleted = targetDeletedState;
                          });
                        }
                      });
                    }

                    // Apply to telefones array
                    if (e.telefones) {
                      e.telefones.forEach((t: any) => {
                        t.is_deleted = targetDeletedState;
                        if (t.value) {
                          Object.values(t.value).forEach((campo: any) => {
                            if (campo) campo.is_deleted = targetDeletedState;
                          });
                        }
                      });
                    }

                    // Apply to enderecos array
                    if (e.enderecos) {
                      e.enderecos.forEach((end: any) => {
                        end.is_deleted = targetDeletedState;
                        if (end.value) {
                          Object.values(end.value).forEach((campo: any) => {
                            if (campo) campo.is_deleted = targetDeletedState;
                          });
                        }
                      });
                    }

                    // Apply to generic lists
                    const genericListKeys = Object.keys(e).filter(key =>
                      key !== 'razao_social' &&
                      key !== 'detalhes' &&
                      key !== 'telefones' &&
                      key !== 'emails' &&
                      key !== 'enderecos' &&
                      Array.isArray(e[key])
                    );

                    genericListKeys.forEach(key => {
                      if (Array.isArray(e[key])) {
                        e[key].forEach((item: any) => {
                          item.is_deleted = targetDeletedState;
                          if (item.value && typeof item.value === 'object') {
                            Object.keys(item.value).forEach(fieldKey => {
                              if (item.value[fieldKey]) {
                                item.value[fieldKey].is_deleted = targetDeletedState;
                              }
                            });
                          }
                        });
                      }
                    });
                  }
                },
                testEntryDeleted,
                testSectionDeleted,
                calculateDataCount
              )
            }
          >
            <CustomReadOnlyInputField
              label={entry.razao_social.label.toUpperCase()}
              colorClass="bg-primary"
              labelTextClass="text-accent"
              value={parseValue(entry.razao_social.value)}
              tooltip={renderSourceTooltip(entry.razao_social.source)}
            />
          </CustomGridItem>
        </CustomGridContainer>
      );
    },

    detalhes: (entry) => {
      if (!entry?.detalhes) return null;
      const subs = Object.entries(entry.detalhes).filter(([, v]) =>
        includeKey((v as any).is_deleted)
      );
      if (!subs.length) return null;
      const idx = idxMap.get(entry)!;

      return (
        <CustomGridContainer
          cols={2}
          columnFirst
          key={`detalhes-${idx}`}
          className="mb-6"
        >
          {subs.map(([fieldKey, val]) => (
            <CustomGridItem
              key={fieldKey}
              cols={1}
              onToggleField={() =>
                actions.updateSectionEntries!(
                  sectionTitle,
                  (e: any, i?: number) => {
                    if (i === idx) {
                      const d = e.detalhes?.[fieldKey];
                      if (d) d.is_deleted = !d.is_deleted;
                    }
                  },
                  testDetalhesDeleted,
                  testSectionDeleted,
                  calculateDataCount
                )
              }
            >
              <CustomReadOnlyInputField
                label={translatePropToLabel((val as any).label || fieldKey).toUpperCase()}
                value={parseValue(String((val as any).value))}
                tooltip={renderSourceTooltip((val as any).source)}
              />
            </CustomGridItem>
          ))}
        </CustomGridContainer>
      );
    },

    emails: (entry) => {
      if (!entry?.emails?.length || !shouldIncludeList(entry.emails)) return null;
      const idx = idxMap.get(entry)!;

      const blocks = entry.emails
        .map((email, i) => ({ bloco: email, idx: i }))
        .filter(({ bloco }) => shouldIncludeNestedBlock(bloco));

      if (!blocks.length) return null;

      return (
        <CustomGridContainer cols={1} key={`emails-${idx}`} className="mb-4">
          <CustomGridItem
            cols={1}
            containerClassName="w-fit pr-12"
            onToggleField={() => onToggleListTitle(idx, 'emails')}
          >
            <ReportsCustomLabel
              label="EMAILS"
              colorClass="bg-primary"
            />
          </CustomGridItem>
          {blocks.map(({ bloco, idx: origIdx }, blockRenderIdx) => (
            <div key={`email-${idx}-${origIdx}`} className="mb-4">
              <CustomGridItem
                cols={1}
                containerClassName="w-fit pr-12 pb-2"
                onToggleField={() => onToggleNestedBlock(idx, 'emails', origIdx)}
              >
                <ReportsCustomLabel
                  label={`EMAIL ${!isTrash ? blockRenderIdx + 1 : ""}`}
                  colorClass="bg-border"
                />
              </CustomGridItem>
              <div className="pl-5">
                <CustomGridContainer cols={2}>
                  {Object.entries(bloco.value)
                    .filter(([_, v]: any) =>
                      isTrash ? v.is_deleted : !v.is_deleted
                    )
                    .map(([fieldKey, fieldValue]: any, index) => (
                      <CustomGridItem
                        key={`email-${idx}-${origIdx}-${fieldKey}`}
                        cols={1}
                        className="py-1"
                        onToggleField={() => onToggleNestedField(idx, 'emails', origIdx, fieldKey)}
                      >
                        <CustomReadOnlyInputField
                          label={translatePropToLabel(
                            fieldValue.label || fieldKey
                          ).toUpperCase()}
                          colorClass="bg-border"
                          value={String(fieldValue.value || "")}
                          isFirstLabelList={index === 0}
                          icon={index === 0 ? <MdOutlineSubdirectoryArrowRight size={16} /> : null}
                          tooltip={renderSourceTooltip(fieldValue.source)}
                        />
                      </CustomGridItem>
                    ))}
                </CustomGridContainer>
              </div>
            </div>
          ))}
        </CustomGridContainer>
      );
    },

    telefones: (entry) => {
      if (!entry?.telefones?.length || !shouldIncludeList(entry.telefones)) return null;
      const idx = idxMap.get(entry)!;

      const blocks = entry.telefones
        .map((t, i) => ({ bloco: t, idx: i }))
        .filter(({ bloco }) => shouldIncludeNestedBlock(bloco));

      if (!blocks.length) return null;

      return (
        <CustomGridContainer cols={1} key={`telefones-${idx}`} className="mb-4">
          <CustomGridItem
            cols={1}
            containerClassName="w-fit pr-12"
            onToggleField={() => onToggleListTitle(idx, 'telefones')}
          >
            <ReportsCustomLabel
              label="TELEFONES"
              colorClass="bg-primary"
            />
          </CustomGridItem>
          <CustomGridContainer cols={2}>
            {blocks.map(({ bloco, idx: origIdx }, blockRenderIdx) => (
              <div key={`tel-${idx}-${origIdx}`} className="mb-4">
                <CustomGridItem
                  cols={1}
                  containerClassName="w-fit pr-12 pb-2"
                  onToggleField={() => onToggleNestedBlock(idx, 'telefones', origIdx)}
                >
                  <ReportsCustomLabel
                    label={`TELEFONE ${!isTrash ? blockRenderIdx + 1 : ""}`}
                    colorClass="bg-border"
                  />
                </CustomGridItem>
                <div className="pl-5">
                  <CustomGridContainer cols={1}>
                    {Object.entries(bloco.value)
                      .filter(([_, v]: any) =>
                        isTrash ? v.is_deleted : !v.is_deleted
                      )
                      .map(([fieldKey, fieldValue]: any, index) => (
                        <CustomGridItem
                          key={`tel-${idx}-${origIdx}-${fieldKey}`}
                          cols={1}
                          className="py-1"
                          onToggleField={() => onToggleNestedField(idx, 'telefones', origIdx, fieldKey)}
                        >
                          <CustomReadOnlyInputField
                            label={translatePropToLabel(
                              fieldValue.label || fieldKey
                            ).toUpperCase()}
                            colorClass="bg-border"
                            value={String(fieldValue.value || "")}
                            isFirstLabelList={index === 0}
                            icon={index === 0 ? <MdOutlineSubdirectoryArrowRight size={16} /> : null}
                            tooltip={renderSourceTooltip(fieldValue.source)}
                          />
                        </CustomGridItem>
                      ))}
                  </CustomGridContainer>
                </div>
              </div>
            ))}
          </CustomGridContainer>
        </CustomGridContainer>
      );
    },

    enderecos: (entry) => {
      if (!entry?.enderecos?.length || !shouldIncludeList(entry.enderecos)) return null;
      const idx = idxMap.get(entry)!;

      const blocks = entry.enderecos
        .map((end, i) => ({ bloco: end, idx: i }))
        .filter(({ bloco }) => shouldIncludeNestedBlock(bloco));

      if (!blocks.length) return null;

      return (
        <CustomGridContainer cols={1} key={`enderecos-${idx}`} className="mb-4">
          <CustomGridItem
            cols={1}
            containerClassName="w-fit pr-12"
            onToggleField={() => onToggleListTitle(idx, 'enderecos')}
          >
            <ReportsCustomLabel
              label="ENDEREÇOS"
              colorClass="bg-primary"
            />
          </CustomGridItem>

          {blocks.map(({ bloco, idx: origIdx }, blockRenderIdx) => (
            <div key={`end-${idx}-${origIdx}`} className="mb-4">
              <CustomGridItem
                cols={1}
                containerClassName="w-fit pr-12 pb-2"
                onToggleField={() => onToggleNestedBlock(idx, 'enderecos', origIdx)}
              >
                <ReportsCustomLabel
                  label={`ENDEREÇO ${!isTrash ? blockRenderIdx + 1 : ""}`}
                  colorClass="bg-border"
                />
              </CustomGridItem>
              <div className="pl-5">
                <CustomGridContainer cols={2}>
                  {Object.entries(bloco.value)
                    .filter(([_, v]: any) =>
                      isTrash ? v.is_deleted : !v.is_deleted
                    )
                    .map(([fieldKey, fieldValue]: any, index) => (
                      <CustomGridItem
                        key={`end-${idx}-${origIdx}-${fieldKey}`}
                        cols={1}
                        className="py-1"
                        onToggleField={() => onToggleNestedField(idx, 'enderecos', origIdx, fieldKey)}
                      >
                        <CustomReadOnlyInputField
                          label={translatePropToLabel(
                            fieldValue.label || fieldKey
                          ).toUpperCase()}
                          colorClass="bg-border"
                          value={String(fieldValue.value || "")}
                          isFirstLabelList={index === 0}
                          icon={index === 0 ? <MdOutlineSubdirectoryArrowRight size={16} /> : null}
                          tooltip={renderSourceTooltip(fieldValue.source)}
                        />
                      </CustomGridItem>
                    ))}
                </CustomGridContainer>
              </div>
            </div>
          ))}
        </CustomGridContainer>
      );
    },
  };

  const renderGenericList = (entry: Sociedade, listKey: string): React.ReactElement | null => {
    const list = (entry as any)[listKey];
    if (!Array.isArray(list) || list.length === 0) return null;

    const idx = idxMap.get(entry)!;

    const filteredItems = list.filter((item: any) => {
      if (item.value && typeof item.value === 'object') {
        return Object.values(item.value).some((field: any) => includeKey(field.is_deleted));
      }
      return includeKey(item.is_deleted);
    });

    if (filteredItems.length === 0) return null;

    const displayTitle = listKey
      .split(' ')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');

    return (
      <CustomGridContainer cols={1} key={`${listKey}-${idx}`} className="mb-4">
        <CustomGridItem
          cols={1}
          containerClassName="w-fit pr-12"
          onToggleField={() => onToggleGenericListTitle(idx, listKey)}
        >
          <ReportsCustomLabel
            label={displayTitle.toUpperCase()}
            colorClass="bg-primary"
          />
        </CustomGridItem>
        {filteredItems.map((item: any, renderIdx: number) => {
          const originalIdx = list.indexOf(item);

          if (item.value && typeof item.value === 'object') {
            return (
              <div key={`${listKey}-${idx}-${originalIdx}`} className="mb-4">
                <CustomGridItem
                  cols={1}
                  containerClassName="w-fit pr-12 pb-2"
                  onToggleField={() => onToggleGenericListItem(idx, listKey, originalIdx)}
                >
                  <ReportsCustomLabel
                    label={`${displayTitle.toUpperCase()} ${!isTrash ? renderIdx + 1 : ""}`}
                    colorClass="bg-border"
                  />
                </CustomGridItem>
                <div className="pl-5">
                  <CustomGridContainer cols={2}>
                    {Object.entries(item.value)
                      .filter(([_, fieldValue]: any) => includeKey(fieldValue.is_deleted))
                      .map(([fieldKey, fieldValue]: any, index) => (
                        <CustomGridItem
                          key={`${listKey}-${originalIdx}-${fieldKey}`}
                          cols={1}
                          className="py-1"
                          onToggleField={() => onToggleGenericListField(idx, listKey, originalIdx, fieldKey)}
                        >
                          <CustomReadOnlyInputField
                            label={translatePropToLabel(fieldValue.label || fieldKey).toUpperCase()}
                            value={parseValue(String(fieldValue.value || ""))}
                            isFirstLabelList={index === 0}
                            icon={index === 0 ? <MdOutlineSubdirectoryArrowRight size={16} /> : null}
                            tooltip={renderSourceTooltip(fieldValue.source)}
                          />
                        </CustomGridItem>
                      ))}
                  </CustomGridContainer>
                </div>
              </div>
            );
          }
          return null;
        })}
      </CustomGridContainer>
    );
  };

  const validateKeys = (keys: Array<keyof Sociedade>): boolean => {
    return keys.some((campo) => campo in formatByKey);
  };

  const renderSingleItem = (entry: Sociedade): React.ReactElement[] => {
    const keys = Object.keys(entry) as Array<keyof Sociedade>;

    if (!validateKeys(keys)) {
      console.warn(`[Seção ${sectionTitle}] Chaves inválidas:`, keys);
    }

    const orderedKeys: Array<string> = [
      'razao_social',
      'detalhes',
      'telefones',
      'enderecos',
      'emails'
    ];

    const filteredKeys = orderedKeys.filter(key => keys.includes(key as keyof Sociedade));

    const genericListKeys = keys.filter(key =>
      !orderedKeys.includes(key as string) && Array.isArray((entry as any)[key])
    );

    const elements: React.ReactElement[] = [];

    // Render known keys first
    filteredKeys.forEach((chave) => {
      const element = formatByKey[chave]?.(entry);
      if (element) elements.push(element);
    });

    // Render generic lists
    genericListKeys.forEach((listKey) => {
      const element = renderGenericList(entry, listKey as string);
      if (element) elements.push(element);
    });

    return elements;
  };

  const render = (dataArray: Sociedade[]): React.ReactElement[] => {
    if (!Array.isArray(dataArray)) {
      console.warn(`[Seção ${sectionTitle}] esperava array, mas recebeu:`, typeof dataArray);
      return [];
    }

    const filteredData = dataArray.filter((entry) => {
      const elements = renderSingleItem(entry);
      return elements.length > 0;
    });

    if (filteredData.length === 0) {
      return [];
    }

    const allElements: React.ReactElement[] = [];

    filteredData.forEach((entry, index) => {
      const elements = renderSingleItem(entry);

      if (filteredData.length > 1) {
        const isLastItem = index === filteredData.length - 1;
        allElements.push(
          <div
            key={`pessoa-relacionada-${index}`}
            className={`relative mb-8 pb-1 ${isLastItem ? '!mb-0 !pb-0' : ''}`}
          >
            {elements}
            {!isLastItem && <ItemSeparator />}
          </div>
        );
      } else {
        allElements.push(...elements);
      }
    });

    return allElements;
  };

  const deleteSectionEntries = () => {
    const updater = actions.updateSectionEntries;
    if (!updater) return;

    // No modo lixeira, restaura (is_deleted = false)
    // No modo normal, deleta (is_deleted = true)
    const targetDeletedState = mode !== "trash";

    updater(
      sectionTitle,
      entry => {
        // Marca todos os campos como deletado/restaurado
        if (entry.razao_social) {
          entry.razao_social.is_deleted = targetDeletedState;
        }
        if (entry.detalhes) {
          Object.values(entry.detalhes).forEach((detalhe: any) => {
            if (detalhe) detalhe.is_deleted = targetDeletedState;
          });
        }
        if (entry.emails) {
          entry.emails.forEach((email: any) => {
            email.is_deleted = targetDeletedState;
            if (email.value) {
              Object.values(email.value).forEach((campo: any) => {
                if (campo) campo.is_deleted = targetDeletedState;
              });
            }
          });
        }
        if (entry.telefones) {
          entry.telefones.forEach((t: any) => {
            t.is_deleted = targetDeletedState;
            if (t.value) {
              Object.values(t.value).forEach((campo: any) => {
                if (campo) campo.is_deleted = targetDeletedState;
              });
            }
          });
        }
        if (entry.enderecos) {
          entry.enderecos.forEach((end: any) => {
            end.is_deleted = targetDeletedState;
            if (end.value) {
              Object.values(end.value).forEach((campo: any) => {
                if (campo) campo.is_deleted = targetDeletedState;
              });
            }
          });
        }

        const genericListKeys = Object.keys(entry).filter(key =>
          key !== 'razao_social' &&
          key !== 'detalhes' &&
          key !== 'telefones' &&
          key !== 'emails' &&
          key !== 'enderecos' &&
          Array.isArray(entry[key])
        );

        genericListKeys.forEach(key => {
          if (Array.isArray(entry[key])) {
            entry[key].forEach((item: any) => {
              item.is_deleted = targetDeletedState;
              if (item.value && typeof item.value === 'object') {
                Object.values(item.value).forEach((field: any) => {
                  if (field && typeof field === 'object' && 'is_deleted' in field) {
                    field.is_deleted = targetDeletedState;
                  }
                });
              }
            });
          }
        });
      },
      testEntryDeleted,
      testSectionDeleted,
      calculateDataCount
    );
  };

  return {
    validateKeys,
    formatByKey,
    render,
    testEntryDeleted,
    testSectionDeleted,
    deleteSectionEntries,
  };
}
