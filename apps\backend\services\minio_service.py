import json
import logging
from datetime import datetime, timedelta, timezone
from io import BytesIO

from core.config import minio_client
from exceptions.business_exceptions import MinioOperationError

logger = logging.getLogger(__name__)


async def save_to_minio(bucket_name: str, object_name: str, data: dict, user_id: str):
    logger.info("[save_to_minio][user(%s)] Saving object %s to bucket %s", user_id, object_name, bucket_name)
    try:
        if not minio_client.bucket_exists(bucket_name):
            logger.warning("[save_to_minio][user(%s)] Bucket %s not found. Creating...", user_id, bucket_name)
            minio_client.make_bucket(bucket_name)

        json_bytes = json.dumps(data, indent=2).encode("utf-8")
        json_stream = BytesIO(json_bytes)

        minio_client.put_object(
            bucket_name,
            object_name,
            data=json_stream,
            length=len(json_bytes),
            content_type="application/json"
        )
        logger.info("[save_to_minio][user(%s)] Upload completed", user_id)

    except Exception as e:
        logger.warning("[save_to_minio] Failed to save object: %s", e)
        # raise MinioOperationError("save", str(e))



async def load_from_minio(bucket_name: str, object_name: str, user_id: str) -> dict | None:
    logger.info("[load_from_minio][user(%s)] Loading object %s", user_id, object_name)
    try:
        response = minio_client.get_object(bucket_name, object_name)
        data = json.loads(response.read())
        data_keys = list(data.keys()) if isinstance(data, dict) else type(data)
        logger.info(f"[load_from_minio][user({user_id})] Retrieved object with keys: {data_keys}")

        return data
    except Exception as e:
        logger.warning("[load_from_minio][user(%s)] Failed to load object %s: %s", user_id, object_name, e)
        return None
        # raise MinioOperationError("load", str(e))


async def delete_from_minio(bucket_name: str, object_name: str, user_id: str) -> dict | None:
    logger.info("[delete_from_minio][user(%s)] Deleting object %s", user_id, object_name)
    try:

        minio_client.remove_object(bucket_name, object_name)
        minio_client.remove_object("reports", object_name)
        logger.info("[delete_from_minio][user(%s)] Object %s deleted after load", user_id, object_name)

    except Exception as e:
        logger.warning("[delete_from_minio][user(%s)] Failed to delete object %s: %s", user_id, object_name, e)
        # raise MinioOperationError("delete", str(e))


# Temporary storage functions for PDF data
TEMP_PDF_BUCKET = "temp-pdf-data"
GENERATED_PDF_BUCKET = "generated-pdfs"

async def store_temp_pdf_data(data_reference: str, pdf_data: dict) -> str:
    """Store temporary PDF data in MinIO"""
    logger.info("[store_temp_pdf_data] Storing temporary PDF data with reference: %s", data_reference)
    try:
        if not minio_client.bucket_exists(TEMP_PDF_BUCKET):
            logger.info("[store_temp_pdf_data] Creating temporary PDF bucket: %s", TEMP_PDF_BUCKET)
            minio_client.make_bucket(TEMP_PDF_BUCKET)

        json_bytes = json.dumps(pdf_data, indent=2).encode("utf-8")
        json_stream = BytesIO(json_bytes)

        minio_client.put_object(
            TEMP_PDF_BUCKET,
            data_reference,
            data=json_stream,
            length=len(json_bytes),
            content_type="application/json"
        )

        logger.info("[store_temp_pdf_data] Temporary PDF data stored successfully")
        return data_reference

    except Exception as e:
        logger.error("[store_temp_pdf_data] Failed to store temporary PDF data: %s", e)
        raise MinioOperationError("store_temp", str(e))


async def load_temp_pdf_data(data_reference: str) -> dict | None:
    """Load temporary PDF data from MinIO"""
    logger.info("[load_temp_pdf_data] Loading temporary PDF data with reference: %s", data_reference)
    try:
        response = minio_client.get_object(TEMP_PDF_BUCKET, data_reference)
        pdf_data = json.loads(response.read())

        logger.info("[load_temp_pdf_data] Temporary PDF data loaded successfully")
        return pdf_data

    except Exception as e:
        if hasattr(e, 'code') and e.code == 'NoSuchKey':
            logger.warning("[load_temp_pdf_data] Temporary PDF data not found: %s", data_reference)
            return None
        logger.error("[load_temp_pdf_data] Failed to load temporary PDF data %s: %s", data_reference, e)
        raise MinioOperationError("load_temp", str(e))


async def delete_temp_pdf_data(data_reference: str) -> None:
    """Delete temporary PDF data from MinIO"""
    logger.info("[delete_temp_pdf_data] Deleting temporary PDF data with reference: %s", data_reference)
    try:
        minio_client.remove_object(TEMP_PDF_BUCKET, data_reference)
        logger.info("[delete_temp_pdf_data] Temporary PDF data deleted successfully")
    except Exception as e:
        logger.error("[delete_temp_pdf_data] Failed to delete temporary PDF data %s: %s", data_reference, e)
        # Don't raise exception for cleanup operations
        pass


# PDF storage functions
async def store_generated_pdf(pdf_reference: str, pdf_buffer: bytes, filename: str) -> str:
    """Store generated PDF in MinIO"""
    logger.info("[store_generated_pdf] Storing generated PDF with reference: %s", pdf_reference)
    try:
        if not minio_client.bucket_exists(GENERATED_PDF_BUCKET):
            logger.info("[store_generated_pdf] Creating generated PDF bucket: %s", GENERATED_PDF_BUCKET)
            minio_client.make_bucket(GENERATED_PDF_BUCKET)

        metadata = {
            'filename': filename,
            'content-type': 'application/pdf'
        }

        pdf_stream = BytesIO(pdf_buffer)

        minio_client.put_object(
            GENERATED_PDF_BUCKET,
            pdf_reference,
            data=pdf_stream,
            length=len(pdf_buffer),
            content_type="application/pdf",
            metadata=metadata
        )

        logger.info("[store_generated_pdf] Generated PDF stored successfully. Size: %d bytes", len(pdf_buffer))
        return pdf_reference

    except Exception as e:
        logger.error("[store_generated_pdf] Failed to store generated PDF: %s", e)
        raise MinioOperationError("store_pdf", str(e))


async def load_generated_pdf(pdf_reference: str) -> tuple[bytes, str] | None:
    """Load generated PDF from MinIO and return PDF bytes and filename"""
    logger.info("[load_generated_pdf] Loading generated PDF with reference: %s", pdf_reference)
    try:
        obj_stat = minio_client.stat_object(GENERATED_PDF_BUCKET, pdf_reference)
        filename = obj_stat.metadata.get('filename', 'report.pdf')

        response = minio_client.get_object(GENERATED_PDF_BUCKET, pdf_reference)
        pdf_bytes = response.read()

        logger.info("[load_generated_pdf] Generated PDF loaded successfully. Size: %d bytes", len(pdf_bytes))
        return pdf_bytes, filename

    except Exception as e:
        if hasattr(e, 'code') and e.code == 'NoSuchKey':
            logger.warning("[load_generated_pdf] Generated PDF not found: %s", pdf_reference)
            return None
        logger.error("[load_generated_pdf] Failed to load generated PDF %s: %s", pdf_reference, e)
        raise MinioOperationError("load_pdf", str(e))


async def delete_generated_pdf(pdf_reference: str) -> None:
    """Delete generated PDF from MinIO"""
    logger.info("[delete_generated_pdf] Deleting generated PDF with reference: %s", pdf_reference)
    try:
        minio_client.remove_object(GENERATED_PDF_BUCKET, pdf_reference)
        logger.info("[delete_generated_pdf] Generated PDF deleted successfully")
    except Exception as e:
        logger.error("[delete_generated_pdf] Failed to delete generated PDF %s: %s", pdf_reference, e)
        # Don't raise exception for cleanup operations
        pass
        # return None
        # raise MinioOperationError("delete", str(e))
