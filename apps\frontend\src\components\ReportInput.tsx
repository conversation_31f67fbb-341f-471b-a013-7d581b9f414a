import { Input } from "@snap/design-system";
import Inputmask from "inputmask";
import { useEffect, useRef } from "react";

interface ReportInputProps {
  setInputValue: (value: string) => void;
  reportType: string;
  inputValue: string;
  documentType?: string;
}

interface Masks {
  [key: string]: string;
}

export function ReportInput({
  setInputValue,
  reportType,
  inputValue,
  documentType,
}: ReportInputProps) {
  const inputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    if (!inputRef.current) return;

    const masks: Masks = {
      cpf: "999.999.999-99",
      cnpj: "99.999.999/9999-99",
      email: "",
      telefone: "(99) 99999-9999",
    };

    const maskType = reportType === "relacoes" ? documentType : reportType;
    const maskPattern = maskType ? masks[maskType] : "";

    Inputmask(maskPattern, {
      placeholder: "_",
      showMaskOnHover: false,
      autoUnmask: true,
    }).mask(inputRef.current);
  }, [reportType, documentType]);

  const getPlaceholder = () => {
    if (reportType === "relacoes" && documentType) {
      return `Digite o ${documentType.toUpperCase()}`;
    }
    return reportType ? `Digite o ${reportType?.toUpperCase()}` : "";
  };

  return (
    <Input
      ref={inputRef}
      value={inputValue}
      onChange={(e) => setInputValue(e.target.value)}
      className="w-full"
      placeholder={getPlaceholder()}
      disabled={!reportType || (reportType === "relacoes" && !documentType)}
      data-testid="input-report-value"
    />
  );
}
