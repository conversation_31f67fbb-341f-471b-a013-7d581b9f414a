import logging
import os
from enum import Enum

MINIO_ENDPOINT = os.environ.get('MINIO_ENDPOINT', 'http://minio:9000')
MINIO_ACCESS_KEY = os.environ.get('MINIO_ACCESS_KEY', 'admin')
MINIO_SECRET_KEY = os.environ.get('MINIO_SECRET_KEY', 'password')
DB_USER = os.environ.get('DB_USER', 'keycloak')
DB_PASS = os.environ.get('DB_PASS', 'keycloak')
DB_NAME = os.environ.get('DB_NAME', 'keycloak')
DB_HOST = os.environ.get('DB_HOST', 'postgres')
DB_PORT = os.environ.get('DB_PORT', '5432')
SPARK_CHECKPOINT_REPORTS = os.environ.get('SPARK_CHECKPOINT_REPORTS', '/tmp/spark_checkpoint_reports')
MASKED_VALUE = '*'
API_ERROR_MESSAGE_KEY = 'error_message'
NO_RESULTS_API_MESSAGE = "Nao foram encontrados resultados!"
null_values = "NAO DECLARADOS"


class ReportKeys:
    PESSOA = "pessoa"
    ADVOGADO = 'advogado'
    SERVIDOR = 'servidor publico'
    EMPRESA = "empresa"
    PROCESSO = "processos"
    CPF = "cpf"
    CNPJ = "cnpj"
    razao_social = 'razão social'
    nome_completo = 'nome_completo'
    REPORT_TYPE = "report_type"
    SEARCH_ARGS = "report_search_args"
    METADATA = "metadata"
    ENDPOINT = "endpoint"
    DATA = "data"
    TELEFONE = "telefone"
    EMAIL_ENTITY = "email"
    EMAIL_ENTRY = 'endereco email'
    NUMERO = "numero"
    REMUNERACAO = 'remuneracao'
    ENDERECO = 'endereço'
    CEP = 'cep ou cep'
    DIARIO_OFICIAL = 'diário oficial'
    PAI = 'provedor de aplicacoes de internet'
    PARTIDO_POLITICO = "partido politico"
    CANDIDATO = 'candidato'
    VINCULO = 'vinculo'
    MANDADO = 'mandado'
    PENA = 'pena'
    RECURSOS_RECEBIDOS = "despesas publicas recursos recebidos"
    EMPRESA_JUCESP = 'empresa jucesp'
    IMAGEM = 'imagem'
    ALIAS = 'alias'
    URL = 'url'
    GENERIC_REDE_SOCIAL = 'perfil de rede social'
    AREA_CODE = 'área code'
    COUNTRY_CODE = 'country code'

class FormattableEntity(Enum):
    ENDERECO = ReportKeys.ENDERECO

    @property
    def main_document(self):
        """Returns the main entity type for this report"""
        return {
            FormattableEntity.ENDERECO: ReportKeys.CEP,
        }[self]

class PreProcessEntity(Enum):
    TELEFONE = ReportKeys.TELEFONE

class CacheableEntityTypes(Enum):
    """Enum for entity types to improve type safety"""
    PESSOA = ReportKeys.PESSOA
    EMPRESA = ReportKeys.EMPRESA

    @property
    def main_document(self):
        """Returns the main entity type for this report"""
        return {
            CacheableEntityTypes.PESSOA: ReportKeys.CPF,
            CacheableEntityTypes.EMPRESA: ReportKeys.CNPJ,
            # ReportType.PHONE: [ReportKeys.PESSOA, ReportKeys.EMPRESA],
            # ReportType.EMAIL: [ReportKeys.PESSOA, ReportKeys.EMPRESA],
        }[self]

    @property
    def name(self):
        """Returns the main entity type for this report"""
        return {
            CacheableEntityTypes.PESSOA: ReportKeys.nome_completo,
            CacheableEntityTypes.EMPRESA: ReportKeys.razao_social,
            # ReportType.PHONE: [ReportKeys.PESSOA, ReportKeys.EMPRESA],
            # ReportType.EMAIL: [ReportKeys.PESSOA, ReportKeys.EMPRESA],
        }[self]

class ReportType(Enum):
    """Enum for report types to improve type safety"""
    CNPJ = "cnpj"
    CPF = "cpf"
    PHONE = 'telefone'
    EMAIL = 'email'

    @property
    def main_entity(self):
        """Returns the main entity type for this report"""
        return {
            ReportType.CNPJ: ReportKeys.EMPRESA,
            ReportType.CPF: ReportKeys.PESSOA,
            ReportType.PHONE: [ReportKeys.PESSOA, ReportKeys.EMPRESA],
            ReportType.EMAIL: [ReportKeys.PESSOA, ReportKeys.EMPRESA],
        }[self]

    @property
    def base_data_keys(self):
        return {
            ReportType.CNPJ: [ReportKeys.CNPJ, ReportKeys.razao_social],
            ReportType.CPF: [ReportKeys.CPF, ReportKeys.nome_completo],
            ReportType.PHONE: [ReportKeys.NUMERO],
            ReportType.EMAIL: [ReportKeys.EMAIL_ENTRY],
        }[self]

main_entity_fields = {ReportKeys.PESSOA: ReportKeys.CPF,
                      ReportKeys.EMPRESA: ReportKeys.CNPJ,
                      ReportKeys.TELEFONE: ReportKeys.NUMERO,
                      ReportKeys.EMAIL_ENTITY: ReportKeys.EMAIL_ENTRY,
                      ReportKeys.ENDERECO: ReportKeys.CEP}

rede_social_key_prefix = "perfil "
label_key = "default vinculo"
vinculo_entity_key = "rotulo"
socio_key = "sócio"
nome_orgao_key = "nome do orgão"
p_movimentacoes_key = "movimentações"
do_adicionais_key = 'dados adicionais'
pena_imposta_key = 'pena imposta'
recaptura_key = 'recaptura'
pena_use_keys = [pena_imposta_key, recaptura_key]
do_local_key = 'local'
do_descricao_key = 'descrição'
do_ocorrencia_key = 'ocorrencia'
do_texto_key = 'texto correspondente'
pais_aplicacao_keys = ['nome', 'aplicação']
partido_sigla_key = 'sigla'
qualificacao_societaria = "qualificacao societária"
recursos_recebidos_skip_label_keys = ["Gasto Total"]
empregos_vinculo = 'vinculo empregaticio'
educacional_vinculo = 'vinculo educacional'
parentes_vinculo = 'parente'
outros_contatos_vinculo = 'outros contatos'
parentes_mae_vinculo = 'MAE'
parentes_pai_vinculo = 'PAI'
nome_completo_key = 'nome_completo'
numero_mandado_key = 'número do mandado de prisao'
numero_processo_key = 'número do processo'
vinculo_autor_processo_nao_identificado_key = 'Não Identificado'
razao_social_key = 'razao_social'
other_id_fields = [nome_completo_key, razao_social_key]
secondary_fields = ['data_nascimento', label_key]
age_fields = ['data de nascimento', 'data de fundacao']
alias_key = 'alias'
skip_fields = ['bookmark', "credilink label", "vinculo do credlink",]
spark = None
logger = logging.getLogger(__name__)


class Constants(str, Enum):
    Generic = os.path.join(os.path.dirname(__file__), 'nomes_genericos.json')
    PropToLabel = os.path.join(os.path.dirname(__file__), 'translate_prop_to_label.json')

# to add ['advogado', 'vitima', 'reu', 'interessado', 'recorrido', 'outro', 'recorrente']


