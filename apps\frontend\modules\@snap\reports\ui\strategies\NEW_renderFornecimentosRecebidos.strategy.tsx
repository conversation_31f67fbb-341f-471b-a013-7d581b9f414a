import React, { useMemo } from "react";
import { ArrayRenderStrategy } from "./RenderStrategy";
import { CustomGridContainer } from "../components/CustomGridContainer";
import { CustomGridItem } from "../components/CustomGridItem";
import { parseValue, translatePropToLabel } from "../../helpers";
import { useReportActions, useReportMode, useReportSections } from "../../context/ReportContext";
import { MdOutlineSubdirectoryArrowRight } from "react-icons/md";
import { CustomReadOnlyInputField } from "../components/CustomReadOnlyInput";
import { renderSourceTooltip } from "./helpers.strategy";
import { GridItem } from "@snap/design-system";
import { ReportsCustomLabel } from "../components/ReportsCustomLabel";
import { FornecimentoRecebido } from "../../model/FornecimentosRecebidos";
import { ItemSeparator } from "../components/ItemSeparator";

export function useRenderFornecimentosRecebidos(
  sectionTitle: string
): ArrayRenderStrategy<FornecimentoRecebido> {
  const actions = useReportActions();
  const mode = useReportMode();
  const isTrash = mode === "trash";

  const section = useReportSections().find((s) => s.title === sectionTitle);
  const idxMap = useMemo(() => {
    const m = new WeakMap<FornecimentoRecebido, number>();
    section?.data.forEach((e, i) => m.set(e as any, i));
    return m;
  }, [section]);

  const includeKey = (isDeleted: boolean) =>
    isTrash ? isDeleted : !isDeleted;

  const testCandidatoDeleted = (e: any) => e.candidato?.is_deleted === true;
  const testDetalhesDeleted = (e: any) =>
    e.detalhes
      ? Object.values(e.detalhes).every((v: any) => v.is_deleted === true)
      : false;
  const testVinculosDeleted = (e: any) =>
    e.vinculos
      ? e.vinculos.every((v: any) =>
        Object.values(v.value || {}).every((val: any) => val.is_deleted === true)
      )
      : true;

  const testEntryDeleted = (entry: any): boolean => {
    const isCandidatoDeleted = testCandidatoDeleted(entry);
    const areDetalhesDeleted = testDetalhesDeleted(entry);
    const areVinculosDeleted = testVinculosDeleted(entry);

    return isCandidatoDeleted && areDetalhesDeleted && areVinculosDeleted;
  };

  const testSectionDeleted = (section: any): boolean =>
    Array.isArray(section.data) && section.data.every(testEntryDeleted);

  const calculateDataCount = (section: any): number => {
    if (!Array.isArray(section.data)) return 0;

    return section.data.reduce((count: number, entry: any) => {
      const isEntryDeleted = testEntryDeleted(entry);
      return isEntryDeleted ? count : count + 1;
    }, 0);
  };

  const shouldIncludeNestedBlock = (item: any) => {
    const vals = Object.values(item.value || {});
    return isTrash
      ? vals.some((v: any) => v.is_deleted === true)
      : vals.some((v: any) => v.is_deleted === false);
  };

  const shouldIncludeList = (arrayItems: any[]) => {
    if (!Array.isArray(arrayItems)) return false;
    return arrayItems.some((item: any) => shouldIncludeNestedBlock(item));
  };

  const onToggleNestedField = (entryIdx: number, arrayKey: string, blockIdx: number, fieldKey: string) => {
    actions.updateSectionEntries!(
      sectionTitle,
      (e: any, i?: number) => {
        if (i === entryIdx) {
          const item = e[arrayKey]?.[blockIdx];
          if (item?.value?.[fieldKey]) {
            item.value[fieldKey].is_deleted = !item.value[fieldKey].is_deleted;

            const allFieldsDeleted = Object.values(item.value).every((campo: any) =>
              campo.is_deleted === true
            );

            item.is_deleted = allFieldsDeleted;
          }
        }
      },
      testEntryDeleted,
      testSectionDeleted,
      calculateDataCount
    );
  };

  const onToggleNestedBlock = (entryIdx: number, arrayKey: string, blockIdx: number) => {
    actions.updateSectionEntries!(
      sectionTitle,
      (e: any, i?: number) => {
        if (i === entryIdx) {
          const item = e[arrayKey]?.[blockIdx];
          if (item?.value) {
            const targetDeletedState = isTrash ? false : true;

            item.is_deleted = targetDeletedState;

            Object.values(item.value).forEach((campo: any) => {
              if (campo) {
                campo.is_deleted = targetDeletedState;
              }
            });
          }
        }
      },
      testEntryDeleted,
      testSectionDeleted,
      calculateDataCount
    );
  };

  const onToggleListTitle = (entryIdx: number, arrayKey: string) => {
    actions.updateSectionEntries!(
      sectionTitle,
      (e: any, i?: number) => {
        if (i === entryIdx) {
          const arrayItems = e[arrayKey];
          if (arrayItems && Array.isArray(arrayItems)) {
            const targetDeletedState = isTrash ? false : true;

            arrayItems.forEach((item: any) => {
              item.is_deleted = targetDeletedState;
              if (item.value) {
                Object.values(item.value).forEach((campo: any) => {
                  if (campo) {
                    campo.is_deleted = targetDeletedState;
                  }
                });
              }
            });
          }
        }
      },
      testEntryDeleted,
      testSectionDeleted,
      calculateDataCount
    );
  };

  const formatByKey: Record<
    string,
    (entry?: FornecimentoRecebido) => React.ReactElement | null
  > = {
    candidato: (entry) => {
      if (!entry?.candidato || !includeKey(entry.candidato.is_deleted || false)) return null;
      const idx = idxMap.get(entry)!;

      return (
        <CustomGridContainer cols={1} key={`candidato-${idx}`}>
          <CustomGridItem
            fullWidth
            className="mb-6"
            onToggleField={() =>
              actions.updateSectionEntries!(
                sectionTitle,
                (e: any, i?: number) => {
                  if (i === idx && e.candidato) {
                    e.candidato.is_deleted = !e.candidato.is_deleted;

                    const targetDeletedState = e.candidato.is_deleted;

                    if (e.detalhes) {
                      Object.values(e.detalhes).forEach((detalhe: any) => {
                        if (detalhe) detalhe.is_deleted = targetDeletedState;
                      });
                    }

                    if (e.vinculos) {
                      e.vinculos.forEach((v: any) => {
                        v.is_deleted = targetDeletedState;
                        if (v.value) {
                          Object.values(v.value).forEach((campo: any) => {
                            if (campo) campo.is_deleted = targetDeletedState;
                          });
                        }
                      });
                    }
                  }
                },
                testEntryDeleted,
                testSectionDeleted,
                calculateDataCount
              )
            }
          >
            <CustomReadOnlyInputField
              label={entry.candidato.label.toUpperCase()}
              colorClass="bg-primary"
              labelTextClass="text-accent"
              value={parseValue(entry.candidato.value)}
              tooltip={renderSourceTooltip(entry.candidato.source)}
            />
          </CustomGridItem>
        </CustomGridContainer>
      );
    },

    detalhes: (entry) => {
      if (!entry?.detalhes) return null;
      const subs = Object.entries(entry.detalhes).filter(([, v]) =>
        includeKey((v as any).is_deleted)
      );
      if (!subs.length) return null;
      const idx = idxMap.get(entry)!;

      return (
        <CustomGridContainer
          cols={2}
          columnFirst
          key={`detalhes-${idx}`}
          className="mb-6"
        >
          {subs.map(([fieldKey, val]) => (
            <CustomGridItem
              key={fieldKey}
              cols={1}
              onToggleField={() =>
                actions.updateSectionEntries!(
                  sectionTitle,
                  (e: any, i?: number) => {
                    if (i === idx) {
                      const d = e.detalhes?.[fieldKey];
                      if (d) d.is_deleted = !d.is_deleted;
                    }
                  },
                  testDetalhesDeleted,
                  testSectionDeleted,
                  calculateDataCount
                )
              }
            >
              <CustomReadOnlyInputField
                label={translatePropToLabel((val as any).label || fieldKey).toUpperCase()}
                value={parseValue(String((val as any).value))}
                tooltip={renderSourceTooltip((val as any).source)}
              />
            </CustomGridItem>
          ))}
        </CustomGridContainer>
      );
    },

    vinculos: (entry) => {
      if (!entry?.vinculos?.length || !shouldIncludeList(entry.vinculos)) return null;
      const idx = idxMap.get(entry)!;

      const blocks = entry.vinculos
        .map((v, i) => ({ bloco: v, idx: i }))
        .filter(({ bloco }) => shouldIncludeNestedBlock(bloco));

      if (!blocks.length) return null;

      return (
        <CustomGridContainer cols={1} key={`vinculos-${idx}`} className="mb-4">
          <CustomGridItem
            cols={1}
            containerClassName="w-fit pr-12"
            onToggleField={() => onToggleListTitle(idx, 'vinculos')}
          >
            <ReportsCustomLabel
              label="FORNECIMENTOS RECEBIDOS"
              colorClass="bg-primary"
            />
          </CustomGridItem>
          {blocks.map(({ bloco, idx: origIdx }, blockRenderIdx) => (
            <div key={`vinc-${idx}-${origIdx}`} className="mb-4">
              <CustomGridItem
                cols={1}
                containerClassName="w-fit pr-12 pb-2"
                onToggleField={() => onToggleNestedBlock(idx, 'vinculos', origIdx)}
              >
                <ReportsCustomLabel
                  label={`FORNECIMENTO ${!isTrash ? blockRenderIdx + 1 : ""}`}
                  colorClass="bg-border"
                />
              </CustomGridItem>
              <div className="pl-5">
                <CustomGridContainer cols={2}>
                  {Object.entries(bloco.value)
                    .filter(([_, v]: any) =>
                      isTrash ? v.is_deleted : !v.is_deleted
                    )
                    .map(([fieldKey, fieldValue]: any, index) => (
                      <CustomGridItem
                        key={`vinc-${idx}-${origIdx}-${fieldKey}`}
                        cols={1}
                        className="py-1"
                        onToggleField={() => onToggleNestedField(idx, 'vinculos', origIdx, fieldKey)}
                      >
                        <CustomReadOnlyInputField
                          label={translatePropToLabel(
                            fieldValue.label || fieldKey
                          ).toUpperCase()}
                          colorClass="bg-border"
                          value={String(fieldValue.value || "")}
                          isFirstLabelList={index === 0}
                          icon={index === 0 ? <MdOutlineSubdirectoryArrowRight size={16} /> : null}
                          tooltip={renderSourceTooltip(fieldValue.source)}
                        />
                      </CustomGridItem>
                    ))}
                </CustomGridContainer>
              </div>
            </div>
          ))}
        </CustomGridContainer>
      );
    },
  };

  const validateKeys = (keys: Array<keyof FornecimentoRecebido>): boolean => {
    return keys.some((campo) => campo in formatByKey);
  };

  const renderSingleItem = (entry: FornecimentoRecebido): React.ReactElement[] => {
    const keys = Object.keys(entry) as Array<keyof FornecimentoRecebido>;

    if (!validateKeys(keys)) {
      console.warn("[Seção Fornecimentos Recebidos] Chaves inválidas:", keys);
    }

    const orderedKeys: Array<keyof FornecimentoRecebido> = [
      'candidato',
      'detalhes',
      'vinculos'
    ];

    const filteredKeys = orderedKeys.filter(key => keys.includes(key));

    return filteredKeys
      .map((chave) => formatByKey[chave]?.(entry))
      .filter((el): el is React.ReactElement => el !== null);
  };

  const render = (dataArray: FornecimentoRecebido[]): React.ReactElement[] => {
    if (!Array.isArray(dataArray)) {
      console.warn("[Seção Fornecimentos Recebidos] Expected array but received:", typeof dataArray);
      return [];
    }

    const filteredData = dataArray.filter((entry) => {
      if (isTrash) {
        return testCandidatoDeleted(entry) ||
          testDetalhesDeleted(entry) ||
          testVinculosDeleted(entry) ||
          (entry.detalhes && Object.values(entry.detalhes).some((v: any) => v.is_deleted === true)) ||
          (entry.vinculos && entry.vinculos.some((v: any) =>
            Object.values(v.value || {}).some((val: any) => val.is_deleted === true)
          ));
      } else {
        const isCompletelyDeleted = testEntryDeleted(entry);
        return !isCompletelyDeleted;
      }
    });

    if (filteredData.length === 0) {
      return [];
    }

    const allElements: React.ReactElement[] = [];

    filteredData.forEach((entry, index) => {
      const elements = renderSingleItem(entry);

      if (filteredData.length > 1) {
        const isLastItem = index === filteredData.length - 1;
        allElements.push(
          <div
            key={`fornecimento-recebido-${index}`}
            className={`relative mb-8 pb-1 ${isLastItem ? '!mb-0 !pb-0' : ''}`}
          >
            {elements}
            {!isLastItem && <ItemSeparator />}
          </div>
        );
      } else {
        allElements.push(...elements);
      }
    });

    return allElements;
  };

  const deleteSectionEntries = () => {
    const updater = actions.updateSectionEntries;
    if (!updater) return;

    const targetDeletedState = mode !== "trash";

    updater(
      sectionTitle,
      entry => {
        if (entry.candidato) {
          entry.candidato.is_deleted = targetDeletedState;
        }
        if (entry.detalhes) {
          Object.values(entry.detalhes).forEach((detalhe: any) => {
            if (detalhe) detalhe.is_deleted = targetDeletedState;
          });
        }
        if (entry.vinculos) {
          entry.vinculos.forEach((v: any) => {
            v.is_deleted = targetDeletedState;
            if (v.value) {
              Object.values(v.value).forEach((campo: any) => {
                if (campo) campo.is_deleted = targetDeletedState;
              });
            }
          });
        }
      },
      testEntryDeleted,
      testSectionDeleted,
      calculateDataCount
    );
  };

  return {
    validateKeys,
    formatByKey,
    render,
    testEntryDeleted,
    testSectionDeleted,
    deleteSectionEntries,
  };
}