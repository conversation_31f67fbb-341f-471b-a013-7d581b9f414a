import { create } from "zustand";
import { ReportData } from "~/types/global";

interface ReportListStoreActions {
    setReportList: (list: ReportData[]) => void;
    incrementReportList: (list: ReportData[]) => void;
    appendReportList: (list: ReportData[]) => void;
    setPage: (page: number) => void;
    incrementPage: () => void;
    setOffset: (offset: number) => void;
    incrementOffeset: () => void;
    setLoadCount: (count: number) => void;
    setSearchFilter: (filter: string) => void;
    clearSearchFilter: () => void;
    resetPagination: () => void;
    setLoadMore: (loadMore: boolean) => void;
    addFolderToPath: (folder: { id: string, name: string }) => void;
    removeFolderFromPath: (folderId: string) => void;
    clearFolderPath: () => void;
}

interface FolderPathItem {
    id: string;
    name: string;
}

interface ReportListStoreState {
    list: ReportData[];
    searchFilter: string;
    loadCount: number;
    page: number;
    offset: number;
    loadMore: boolean;
    folderPath: FolderPathItem[];
    actions: ReportListStoreActions;
}

const useReportListStore = create<ReportListStoreState>((set) => ({
    list: [],
    searchFilter: "",
    loadCount: 25,
    page: 1, // página inicial
    offset: 0,
    loadMore: false,
    folderPath: [],
    actions: {
        // TODO - implementar paginação - front manter o estado inicial da lista e adiciona os resultados subsequentes da API
        setReportList: (list: ReportData[]) => set({ list }),
        incrementReportList: (list: ReportData[]) => set(state => ({ list: [...state.list, ...list] })),
        appendReportList: (newItems: ReportData[]) =>
            set(state => ({ list: [...state.list, ...newItems] })),
        setPage: (page: number) => set({ page }),
        setOffset: (offset: number) => set({ offset }),
        incrementOffeset: () => set(state => ({ offset: state.offset + state.loadCount })),
        incrementPage: () => set(state => ({ page: state.page + 1 })),
        setLoadCount: (count: number) => set(state => ({ loadCount: state.loadCount + count })),
        setSearchFilter: (filter: string) => set({ searchFilter: filter }),
        clearSearchFilter: () => set({ searchFilter: "" }),
        resetPagination: async () => set({ page: 1, loadCount: 25 }),
        setLoadMore: (loadMore: boolean) => set({ loadMore }),
        addFolderToPath: (folder: { id: string, name: string }) => set(state => ({
            folderPath: [...state.folderPath, folder]
        })),
        removeFolderFromPath: (folderId: string) => set(state => ({
            folderPath: state.folderPath.filter(folder => folder.id !== folderId)
        })),
        clearFolderPath: () => set({ folderPath: [] }),
    },
}));

export const useReportList = () =>
    useReportListStore((state) => state.list);
export const useReportListSearchFilter = () =>
    useReportListStore((state) => state.searchFilter);
export const useReportListLoadCount = () =>
    useReportListStore((state) => state.loadCount);
export const useReportListPage = () =>
    useReportListStore((state) => state.page);
export const useReportListLoadMore = () =>
    useReportListStore((state) => state.loadMore);
export const useReportListOffset = () =>
    useReportListStore((state) => state.offset);
export const useFolderPath = () =>
    useReportListStore((state) => state.folderPath);
export const useReportListActions = () =>
    useReportListStore((state) => state.actions);
