import { Button, Input, Text } from "@snap/design-system";
import { Check } from "lucide-react";
import { useState } from "react";
import { AiOutlineLoading3Quarters } from "react-icons/ai";
import { useFolderCRUD } from "~/hooks/useFolderCRUD";

export function RenameFolderDialog({
  folderName,
  onCancel,
  onConfirm,
}: {
  folderName: string;
  onCancel: () => void;
  onConfirm: (newName: string) => void;
}) {
  const [name, setName] = useState(folderName);
  const { renameFolderMutation } = useFolderCRUD();

  return (
    <>
      <Text className="block mb-1">Digite o novo nome da pasta:</Text>
      <Input
        type="text"
        variant="outlined"
        value={name}
        onChange={e => setName(e.target.value)}
        className="rounded-none border-0 w-full border-b dashed pl-0"
        maxLength={100}
        autoFocus
      />
      <div className="flex gap-3 mt-8">
        <Button onClick={onCancel} className="uppercase !bg-transparent">
          Cancelar
        </Button>
        <Button
          onClick={() => onConfirm(name.trim())}
          className="uppercase !bg-foreground !text-background !font-bold disabled:opacity-50"
          icon={renameFolderMutation.isPending ? <AiOutlineLoading3Quarters size={16} /> : <Check size={16} />}
          iconPosition="right"
          disabled={name.trim() === folderName || renameFolderMutation.isPending}
        >
          CONFIRMAR
        </Button>
      </div>
    </>
  );
}
