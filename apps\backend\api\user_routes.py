import logging

logger = logging.getLogger(__name__)

from fastapi import APIRouter, Depends
from sqlalchemy.ext.asyncio import AsyncSession

from database.db import get_db

from services.auth_service import auth_guard
from services.user_service import UserStandaloneService

from utils.jwt_utils import JWTUtils

from core.constants import Roles
from exceptions.business_exceptions import InvitePermissionDeniedError

router = APIRouter()


@router.patch("/accept_terms")
async def accept_terms(
    db: AsyncSession = Depends(get_db),
    user=Depends(auth_guard)):

    logger.info("[accept_terms][user(%s)] Accepting terms.", user)

    token_decoded = JWTUtils(user_jwt=user)

    user_id = token_decoded.get_user_id()
    user_roles = token_decoded.get_user_roles()
    logger.info(f"[accept_terms] Decoded user_id: {user_id}, user_roles: {user_roles}")

    if Roles.accept_terms not in user_roles:
        logger.warning("[accept_terms] Access denied - User %s does not have accept terms role", user_id)
        raise InvitePermissionDeniedError("aceitar termos")

    user_service = UserStandaloneService(db=db, user_id=user_id)
    await user_service.update_user_accept_terms()

    return {"message": "Terms accepted successfully"}



@router.post("/auth_guard")
async def get_user_info(
    db: AsyncSession = Depends(get_db),
    user=Depends(auth_guard)):

    logger.info("[get_user_info][user(%s)] Fetching user info.", user)

    token_decoded = JWTUtils(user_jwt=user)
    user_id = token_decoded.get_user_id()

    user_service = UserStandaloneService(db=db, user_id=user_id)
    user_info = await user_service.get_user_info()

    return {"user_info": user_info}