import { CryptoResult } from "~/hooks/useEncryption";
import { EncryptedPayload, ReportData } from "~/types/global";
import { decryptReportPayload } from "./encryption.helper";
import { FolderData } from "root/domain/entities/folder.model";

/**
 * Decrypts an item that could be either a report or a folder
 */
export const decryptListItem = async (
  item: any,
  decryptData: (data: EncryptedPayload) => Promise<CryptoResult<any>>
): Promise<ReportData | FolderData> => {
  const decryptedItem = await decryptReportPayload(item, decryptData);
  
  if (decryptedItem && "folder_id" in decryptedItem && Array.isArray(decryptedItem.data)) {
    decryptedItem.data = await Promise.all(
      decryptedItem.data.map(dataItem => decryptReportPayload(dataItem, decryptData))
    );
  }
  
  return decryptedItem as ReportData | FolderData;
};