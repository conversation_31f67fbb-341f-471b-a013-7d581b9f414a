import { Button, Text } from "@snap/design-system";
import { Check, Folder, X } from "lucide-react";
import { useMemo } from "react";
import { DataTable, Column } from "~/components/Table";
import { useFolderCRUD } from "~/hooks/useFolderCRUD";
import { FolderListItem, useSelectedFolder, useFolderListActions, useFolderListFilters, useFolderListTotalItems } from "~/store/folderListStore";
import { useDialogActions } from "~/store/dialogStore";
import { AiOutlineLoading3Quarters } from "react-icons/ai";
import { FaFolder } from "react-icons/fa";
import { formatIsoDate } from "~/helpers";

interface MergeFoldersDialogContentProps {
  currentFolderId?: string;
}

export function MergeFoldersDialogContent({ currentFolderId }: MergeFoldersDialogContentProps) {
  const { setSelectedFolder, setPage, setTotalItems } = useFolderListActions();
  const filters = useFolderListFilters();
  const totalItems = useFolderListTotalItems();
  const { useFolderListExceptContainsQuery } = useFolderCRUD();
  const { data, isLoading, error, refetch } = useFolderListExceptContainsQuery(currentFolderId);

  const folderList = data?.folders || [];
  const pagination = data?.pagination;

  if (pagination && pagination.total_items !== totalItems) {
    setTotalItems(pagination.total_items);
  }

  const columns: Column<FolderListItem>[] = useMemo(() => [
    {
      key: "select",
      header: "",
      render: (folder) => (
        <input
          type="radio"
          name="selectedFolder"
          value={folder.id}
          onChange={() => setSelectedFolder(folder.id)}
          className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 focus:ring-blue-500"
        />
      ),
      width: "50px",
    },
    {
      key: "icon",
      header: "",
      render: () => (
        <div className="flex items-center justify-center">
          <FaFolder className="text-yellow-500" size={20} />
        </div>
      ),
      width: "50px",
    },
    {
      key: "name",
      header: "Nome da Pasta",
      render: (folder) => (
        <div className="flex items-center gap-2">
          <Text variant="body-md" className="font-medium">
            {folder.name}
          </Text>
        </div>
      ),
    },
    {
      key: "created_at",
      header: "Data de Criação",
      render: (folder) => (
        <Text variant="body-sm" className="text-gray-600">
          {formatIsoDate(folder.created_at)}
        </Text>
      ),
      width: "150px",
    },
    {
      key: "reports_count",
      header: "Relatórios",
      render: (folder) => (
        <Text variant="body-sm" className="text-gray-600">
          {folder.reports_count || 0}
        </Text>
      ),
      width: "100px",
    },
    {
      key: "folders_count",
      header: "Pastas",
      render: (folder) => (
        <Text variant="body-sm" className="text-gray-600">
          {folder.folders_count || 0}
        </Text>
      ),
      width: "100px",
    },
  ], [setSelectedFolder]);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <AiOutlineLoading3Quarters className="animate-spin text-2xl" />
        <Text variant="body-md" className="ml-2">
          Carregando pastas...
        </Text>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center h-64 gap-4">
        <X className="text-red-500" size={48} />
        <Text variant="body-md" className="text-red-600">
          Erro ao carregar pastas
        </Text>
        <Button onClick={() => refetch()} variant="outline">
          Tentar novamente
        </Button>
      </div>
    );
  }

  if (folderList.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center h-64 gap-4">
        <Folder className="text-gray-400" size={48} />
        <Text variant="body-md" className="text-gray-600">
          Nenhuma pasta disponível para mesclar
        </Text>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <Text variant="body-md" className="text-gray-700">
        Selecione a pasta de destino para mesclar. Todo o conteúdo da pasta atual será movido para a pasta selecionada.
      </Text>
      
      <DataTable
        data={folderList}
        columns={columns}
        pagination={pagination}
        onPageChange={setPage}
        isLoading={isLoading}
      />
    </div>
  );
}

interface MergeFoldersDialogFooterProps {
  folderId: string;
  onMergeSuccess?: () => void;
}

export function MergeFoldersDialogFooter({ folderId, onMergeSuccess }: MergeFoldersDialogFooterProps) {
  const selectedFolder = useSelectedFolder();
  const { closeDialog } = useDialogActions();
  const { clearSelectedFolder } = useFolderListActions();
  const { mergeFoldersMutation } = useFolderCRUD();

  const handleCancel = () => {
    clearSelectedFolder();
    closeDialog();
  };

  const handleMerge = () => {
    if (!selectedFolder) {
      return;
    }

    mergeFoldersMutation.mutate({
      folderId: selectedFolder,
      folderIdToMerge: folderId,
    }, {
      onSuccess: () => {
        if (onMergeSuccess) {
          onMergeSuccess();
        }
        clearSelectedFolder();
      }
    });
  };

  return (
    <div className="flex gap-3">
      <Button
        className="uppercase !bg-transparent"
        onClick={handleCancel}
        disabled={mergeFoldersMutation.isPending}
      >
        Cancelar
      </Button>
      <Button
        className="uppercase !bg-foreground !text-background !font-bold disabled:opacity-60"
        icon={<Check size={16} />}
        iconPosition="right"
        onClick={handleMerge}
        disabled={!selectedFolder || mergeFoldersMutation.isPending}
        loading={mergeFoldersMutation.isPending}
      >
        Mesclar
      </Button>
    </div>
  );
}

export const MergeFoldersDialog = {
  Content: MergeFoldersDialogContent,
  Footer: MergeFoldersDialogFooter,
};
