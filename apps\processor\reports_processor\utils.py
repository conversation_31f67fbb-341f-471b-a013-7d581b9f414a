import json

from reports_processor.constants import MASKED_VALUE, main_entity_fields, logger, Constants, label_key, ReportKeys, \
    vinculo_entity_key

_constants = None
def get_constant(file_path: Constants, key: str, default=None):
    """Load constants from JSON file with caching."""
    global _constants

    if _constants is None:
        _constants = {}

    if file_path.name not in _constants:
        try:
            with open(file_path.value, 'r') as f:
                _constants[file_path.name] = json.load(f)
        except Exception as e:
            logger.error(f"Error loading constants file: {e}")
            return default

    current = _constants[file_path.name]

    if file_path is Constants.PropToLabel:
        # Strip only trailing numbers
        key = key.rstrip('0123456789')

    return current.get(key, default)


def get_dtype(df, colname):
    """Returns the data type of a column."""
    return df.schema[colname].dataType


def _sum_sources(data: dict) -> set:
    total_sources = set()
    for value in data.values():
        if isinstance(value, dict):
            sources = value.get("source")
            if isinstance(sources, set):
                total_sources.update(sources)
    return total_sources

def basic_property_dict(value, label, source, is_deleted=False):

    if len(source) > 1:
        if isinstance(value, dict):
            source = _sum_sources(value) or source
        elif isinstance(value, str) or isinstance(value, int):
            pass
        elif isinstance(value, list):
            for item in value:
                return [basic_property_dict(item, label, source, is_deleted)]
        else:
            logger.warning(f"cannot basic property value type {type(value)}")

    return {"value": value,
            "label": label,
            "source": set(source),
            "is_deleted": is_deleted
            }

def normalize_document(doc: str):
    """Extracts only digits from a document string. If it has @, skip - email"""
    if '@' in doc:
        return doc
    return ''.join(c for c in doc if c.isdigit() or c == MASKED_VALUE)

def normalize_name(name: str) -> str:
    from unidecode import unidecode
    return unidecode(name.strip().lower()) if name else ""

def do_names_match(name1, name2):
    def normalize(value):
        if isinstance(value, str):
            return {normalize_name(value)}
        elif isinstance(value, set):
            return {normalize_name(v) for v in value}
        return set()

    return not normalize(name1).isdisjoint(normalize(name2))

def is_masked_document_match(doc1, doc2):
    """Checks if two documents match, considering masks."""
    is_masked = MASKED_VALUE in doc1 or MASKED_VALUE in doc2
    doc1_digits = normalize_document(doc1)
    doc2_digits = normalize_document(doc2)

    if not is_masked:
        return doc1_digits == doc2_digits

    if len(doc1_digits) != len(doc2_digits):
        return False

    for i in range(len(doc1_digits)):
        # Only compare positions that aren't masked with *
        if doc1_digits[i] == MASKED_VALUE or doc2_digits[i] == MASKED_VALUE:
            continue
        if doc1_digits[i] != doc2_digits[i]:
            return False

    return True


def is_a_match(entity: dict, entity_type, search_value):

    if entity_type not in main_entity_fields:
        return False

    if main_entity_fields[entity_type] not in entity:
        return False

    return is_masked_document_match(search_value, entity[main_entity_fields[entity_type]])


def strip_trailing_digits(key):
    """
    Strips trailing digits from a key.
    Example: 'name123' becomes 'name'
    """
    return key.rstrip('0123456789')


def get_new_key(key, the_dict):
    base_key = strip_trailing_digits(key)
    suffix = 1
    new_key = f"{base_key}{suffix}"

    # Find a unique field name
    while new_key in the_dict:
        suffix += 1
        new_key = f"{base_key}{suffix}"
    return new_key


def normalize_cep(value):
    if not value:
        return value

    try:
        # Try converting to float then to int, handles '30380530.0', 30380530.0
        numeric = int(float(value))
        return str(numeric).zfill(8)
    except (ValueError, TypeError):
        pass

    try:
        # If it's a string like '30.380-530' — remove non-digits
        digits = ''.join(c for c in value if c.isdigit())
        if digits:
            return digits.zfill(8)
    except Exception:
        pass

    return value

def compare_and_update_vinculo(entity, vinculo_value, full_match=True):
    candidates = []

    # Get label_key value
    value = entity.get(label_key)
    if isinstance(value, str):
        candidates.append(value)

    # Get values from VINCULO
    vinculo_data = entity.get(ReportKeys.VINCULO)
    if isinstance(vinculo_data, dict):
        value = vinculo_data.get(vinculo_entity_key)
        if isinstance(value, str):
            candidates.append(value)
    elif isinstance(vinculo_data, list):
        candidates.extend(
            x.get(vinculo_entity_key)
            for x in vinculo_data
            if isinstance(x.get(vinculo_entity_key), str)
        )

    # Match logic
    if full_match:
        match = next((vinculo_value for v in candidates if v.lower() == vinculo_value.lower()), None)
    else:
        vinculo_value_lower = vinculo_value.lower()
        match = next((v for v in candidates if vinculo_value_lower in v.lower()), None)

    # If match found, update label_key and return True
    if match:
        entity[label_key] = match
        return True

    return False

# def compare_vinculo(entity, vinculo_value, full_match=True):
#     entity_value_to_compare = []
#
#     if label_key in entity and type(entity[label_key]) == str:
#         entity_value_to_compare = [entity.get(label_key)]
#
#     if ReportKeys.VINCULO in entity:
#         if type(entity[ReportKeys.VINCULO]) == dict:
#             entity_value_to_compare += [entity.get(ReportKeys.VINCULO).get(vinculo_entity_key)]
#         elif type(entity[ReportKeys.VINCULO]) == list:
#             entity_value_to_compare += [x.get(vinculo_entity_key) for x in entity.get(ReportKeys.VINCULO)]
#
#     if full_match:
#         return any(v == vinculo_value for v in entity_value_to_compare)
#     else:
#         vinculo_value_lower = vinculo_value.lower()
#         return any(vinculo_value_lower in v.lower() for v in entity_value_to_compare)


def get_vinculo(entity):
    labels = []

    if label_key in entity and type(entity[label_key]) == str:
        labels =  [entity.get(label_key)]

    elif ReportKeys.VINCULO in entity and type(entity[ReportKeys.VINCULO]) == dict:
        labels = [entity.get(ReportKeys.VINCULO).get(vinculo_entity_key)]

    elif ReportKeys.VINCULO in entity and type(entity[ReportKeys.VINCULO]) == list:
        labels = [x.get(vinculo_entity_key) for x in entity.get(ReportKeys.VINCULO)]

    if labels:
        return labels[0]

    return ""