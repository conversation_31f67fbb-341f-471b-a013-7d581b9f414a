
services:
  keycloak:
    image: my-keycloak-image
    command: ["start", "--import-realm"]

    deploy:
#      replicas: 1
      resources:
        limits:
          cpus: "0.75"
          memory: "2G"
        reservations:
          cpus: "0.2"
          memory: "1100M"
    restart: unless-stopped

    secrets:
      - keycloak_admin_user
      - keycloak_admin_password
    environment:
      KC_DB: postgres
      KC_DB_URL: ****************************************
      KC_DB_USERNAME: ${DB_NAME}
      KC_DB_PASSWORD: ${DB_PASS}
      KC_PROXY: edge
      KC_HTTP_ENABLED: "true"
      KC_IMPORT: /opt/keycloak/data/import/realm.json
      KC_HOSTNAME_STRICT: "true"
      KC_HOSTNAME: ${KC_HOSTNAME}
      KEYCLOAK_URL: ${KEYCLOAK_URL}
    volumes:
      - keycloak_data:/opt/keycloak/data
      - ./apps/keycloak/realm.json:/opt/keycloak/data/import/realm.json
    networks:
      - mystack-net
    ports:
      - "8080:8080"

volumes:
  keycloak_data:
