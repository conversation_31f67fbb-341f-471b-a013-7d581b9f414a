import logging

logger = logging.getLogger(__name__)

from fastapi import APIRouter, Depends
from sqlalchemy.ext.asyncio import AsyncSession

from database.db import get_db

from services.auth_service import auth_guard
from services.organization_users_service import OrganizationUsersService
from services.user_service import UserStandaloneService
from services.invite_service import InviteService
from services.organization_service import OrganizationService

from utils.jwt_utils import JWTUtils

from core.constants import Roles, InviteFields
from models.enums import invite_status
from schemas.invite_schema import InviteIdOptional
from schemas.organization_schema import OrganizationUpdatePrintSnapLogo
from exceptions.business_exceptions import InvitePermissionDeniedError, NotInSameOrganizationError, PrintSnapLogoPermissionDeniedError

router = APIRouter()


@router.post("/leave_organization")
async def leave_organization(   
    invite_data: InviteIdOptional,
    db: AsyncSession = Depends(get_db),
    user=Depends(auth_guard)
):
    
    token_decoded = JWTUtils(user_jwt=user)
    user_id = token_decoded.get_user_id()
    user_roles = token_decoded.get_user_roles()
    user_id_target = user_id

    if invite_data:
        #remove user from email_invited
        logger.info("[OrganizationEndpoint] User is being remove from organization from invite %s", invite_data.invite_id)
        logger.debug("[OrganizationEndpoint] Invite id: %s", invite_data.invite_id)

        if Roles.remove_organization not in user_roles:
            logger.warning("[OrganizationEndpoint] Access denied - User %s does not have permission to remove from organization access", user_id)
            raise InvitePermissionDeniedError("remover de organização")
        


        invite_service = InviteService(db=db, user_id=user_id)
        invite_data_result = await invite_service.update_invite_status(new_status=invite_status.desvinculado, invite_id=invite_data.invite_id)

        email_invited = invite_data_result.get(InviteFields.email_invited)
        logger.info("[OrganizationEndpoint] Email invited: %s", email_invited)        


        user_service = UserStandaloneService(db=db, user_id=user_id)

        user_target_data = await user_service.get_user_data(email=email_invited)
        user_id_target = user_target_data.user_id

        logger.debug("[OrganizationEndpoint] User id: %s", user_id_target)

        organization_users_service = OrganizationUsersService(db=db, user_id=user_id)
        organization_data = await organization_users_service.get_organization_user()
        organization_id = organization_data.organization_id

        logger.debug("[OrganizationEndpoint] Organization id: %s", organization_id)

        organization_users_service_target = OrganizationUsersService(db=db, user_id=user_id_target)
        organization_data_target = await organization_users_service_target.get_organization_user()
        organization_id_target = organization_data_target.organization_id

        logger.debug("[OrganizationEndpoint] Organization id target: %s", organization_id_target)

        if organization_id != organization_id_target:
            logger.warning("[OrganizationEndpoint] User is not in the same organization as the target user")
            raise NotInSameOrganizationError()
        await organization_users_service_target.leave_organization()

        message = "User removed from organization successfully"

    else:

        logger.info("[OrganizationEndpoint] User %s is trying to leave organization", user_id)
        logger.debug("[OrganizationEndpoint] User roles: %s", user_roles)
        organization_users_service = OrganizationUsersService(db=db, user_id=user_id)

        if Roles.leave_organization not in user_roles:
            logger.warning("[OrganizationEndpoint] Access denied - User %s does not have leave organization access", user_id)
            raise InvitePermissionDeniedError("sair da organização")

        await organization_users_service.leave_organization()

        message = "User removed from organization successfully"


    #remove data of organization to user


    #remove from keycloak

    user_service = UserStandaloneService(db=db, user_id=user_id_target)
    await user_service.update_user()

    return {"message": message}



@router.patch("/print_snap_logo")
async def print_snap_logo(
    print_snap_logo: OrganizationUpdatePrintSnapLogo,
    db: AsyncSession = Depends(get_db),
    user=Depends(auth_guard)
):
    token_decoded = JWTUtils(user_jwt=user)
    user_id = token_decoded.get_user_id()
    user_roles = token_decoded.get_user_roles()

    if Roles.print_snap_logo not in user_roles:
        raise PrintSnapLogoPermissionDeniedError()
    
    organization_users_service = OrganizationUsersService(db=db, user_id=user_id)
    organization_data = await organization_users_service.get_organization_user()
    organization_id = organization_data.organization_id

    organization_service = OrganizationService(db=db, organization_id=organization_id)
    await organization_service.update_organization_print_snap_logo(print_snap_logo=print_snap_logo.print_snap_logo)

    return {"message": "Print snap logo updated successfully"}



# @router.post("/create-organization")
# async def create_organization(
#     organization_info: OrganizationBase,
#     db: AsyncSession = Depends(get_db),
#     user=Depends(auth_guard)
# ):
#     """
#     Create a new organization.    
#     Args:
#         organization_info: Organization details
#         user_creating: First user manager information
#         db: Database session
#         keycloak_admin: Keycloak admin client       
        
#     Raises:
#         HTTPException: If organization or user creation fails
#     """
#     logger.info("[OrganizationEndpoint] Starting organization creation process for '%s'", organization_info.name)
#     user_id = user.get("sub")
#     user_realm_access = user.get("realm_access")
#     user_roles = user_realm_access.get("roles")
#     logger.debug("[OrganizationEndpoint] Organization details: %s",
#         {
#             "name": organization_info.name,
#             "has_image_logo": bool(organization_info.image_logo),
#             "user_id": user_id,
#             "user_roles": user_roles
#         }
#     )

#     if Roles.manager not in user_roles:
#         raise HTTPException(
#             status_code=status.HTTP_403_FORBIDDEN,
#             detail="User does not have manager access."
#         )

#     try:
#         # Create organization
#         logger.info("[OrganizationEndpoint] Creating organization in database")

#         organization_data = OrganizationBase(**vars(organization_info))

#         organization_id = await Organization._insert_organization_into_local_db(db=db, organization_data=organization_data
#                                                                                 , user_id=user_id)

#         logger.info(
#             "[OrganizationEndpoint] Organization created successfully with ID: %s",
#             organization_id
#         )

#         return organization_id
        

#     except HTTPException as he:
#         logger.error(
#             "[OrganizationEndpoint] HTTP error during organization creation: %s",
#             str(he),
#             exc_info=True
#         )
#         raise he
#     except Exception as e:
#         logger.error(
#             "[OrganizationEndpoint] Unexpected error during organization creation: %s",
#             str(e),
#             exc_info=True
#         )
#         raise HTTPException(
#             status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
#             detail=f"Failed to create organization and setup manager: {str(e)}"
#         )



