import { REPORT_CONSTANTS } from "~/helpers/constants";

export interface EncryptedPayload {
  encrypted: string;
  iv: string;
}

export type ReportType = "cpf" | "cnpj" | "email" | "telefone" | null;

type StatusMap = (typeof REPORT_CONSTANTS)["status"];

export type ReportStatusTypes = StatusMap[keyof StatusMap];

export interface ReportStatusProps {
  [REPORT_CONSTANTS.new_report.status_report]: ReportStatusTypes;
  [REPORT_CONSTANTS.new_report.snap_request_id]?: string;
}

export type ReportListItem = {
  [REPORT_CONSTANTS.new_report.report_id]: object;
  [REPORT_CONSTANTS.new_report.report_name]: string;
  [REPORT_CONSTANTS.new_report.report_omitted_nodes]?: any[];
  [REPORT_CONSTANTS.new_report.report_type]: string;
  [REPORT_CONSTANTS.new_report.report_status]: string;
  [REPORT_CONSTANTS.new_report.report_search_args]: object[];
  [REPORT_CONSTANTS.new_report.subject_name]: string;
  [REPORT_CONSTANTS.new_report.subject_mother_name]: string;
  [REPORT_CONSTANTS.new_report.subject_age]: string | number;
  [REPORT_CONSTANTS.new_report.subject_sex]: string;
  [REPORT_CONSTANTS.new_report.creation_at]: string;
  [REPORT_CONSTANTS.new_report.modified_at]: string;
};

interface ProcessedItem {
  type: string;
  title?: string;
  name?: string;
  label?: string;
  value?: any;
  children: number | ProcessedItem[] | null;
  parent: string;
  path: string;
}

//Interface para items (campos simples de input ou accordion groups)
export interface ProcessedItemGroup {
  type: "Accordion" | "TextField" | "Section";
  title?: string;
  name?: string;
  label?: string;
  value?: any;
  children?: number | null;
  parent: string | null;
  path: string;
}

// interface pra o primeiro parse que reorganiza os retornos do reports em novos grupos de acordo com o esperado para a UI
export interface AccordionGroupData {
  subtitle: string;
  origin: string[];
}

// interface que define os grupos de dados que vão ser renderizados na UI (accordion group)
export interface AccordionGroup {
  title: string;
  subtitle: string;
  fields: ProcessedItem[];
}

type ReportDetails = {
  result: {
    creationDate: string;
    creditsUsed: number;
    data: AccordionGroup[];
    lastModified: string;
    omittedNodes: any[];
    reportName: string;
    reportStatus: string;
    reportType: ReportType;
    requestDate: string;
    searchArgs: Record<string, string[]>;
  };
} | null;

export type NormalizeOptions = {
  case?: "lower" | "upper" | "preserve";
  allowSpecialChars?: RegExp;
};

export interface ReportCredits {
  [key: string]: number;
}

export type UserData = {
  user_id: string;
  name: string;
  email: string;
  credits: number;
  saved_reports: number;
  report_types: string[];
  last_login: string;
  image: string | null;
  salt: string | null;
  verifier: EncryptedPayload | null;
  account_type?: string;
  valid_until?: string;
  organization_name?: string | null;
  organization_id?: string | null;
  organization_logo?: string | null;
  next_reset_credits?: string | null;
  report_count?: Record<string, number>;
  credits_minimun?: number | null;
  has_active_invite?: boolean;
  credits_monthly?: number;
  roles?: string[];
  is_deleted?: boolean;
  api_key?: string | null;
  role?: string;
  print_snap_logo?: boolean;
  accept_terms?: boolean;
};

export interface Dictionary {
  [key: string]: string;
}

export type MasonryRenderProps<T> = {
  index: number;
  data: T;
  width: number;
};

export interface EncryptedData {
  encrypted: string;
  iv: string;
}

interface BaseFilterFunctionConfig {
  name: string;
  args?: any;
}

/* FILTRAR DETALHES REPORTS */
interface OriginConfig {
  path: (string | number)[];
  filterFunction: BaseFilterFunctionConfig | null;
  hasMotherName: boolean;
  removeNestedItems: boolean;
  noRenderPropList: string[];
}

interface GroupConfig {
  subtitle: string;
  origins: {
    [originKey: string]: OriginConfig[]; //| OriginConfig[];
  };
}

export type ReportConfig = {
  [groupName: string]: GroupConfig;
};

export type NewReportRequest = {
  report_type: string;
  report_input_value: string;
  report_input_encrypted: EncryptedData;
  user_reports_id?: string;
  parent_folder_id?: string | null;
};

export type NewReportResponse = {
  [REPORT_CONSTANTS.new_report.report_name]: string;
  [REPORT_CONSTANTS.new_report.report_omitted_nodes]: any[];
  [REPORT_CONSTANTS.new_report.report_type]: string;
  [REPORT_CONSTANTS.new_report.report_status]: string;
  [REPORT_CONSTANTS.new_report.report_search_args]: object[];
  [REPORT_CONSTANTS.new_report.subject_name]: string;
  [REPORT_CONSTANTS.new_report.subject_mother_name]: string;
  [REPORT_CONSTANTS.new_report.subject_age]: string | number;
  [REPORT_CONSTANTS.new_report.subject_sex]: string;
  [REPORT_CONSTANTS.new_report.creation_at]: string;
  [REPORT_CONSTANTS.new_report.modified_at]: string;
  [REPORT_CONSTANTS.new_report.data]?: object;
  [REPORT_CONSTANTS.new_report.report_id]: object;
};

export type EncryptedValue = {
  encrypted: string;
  iv: string;
};

export type IconReportType = "cpf" | "cnpj" | "telefone" | "email" | "relacoes";

export type ListItemProps = {
  label: string;
  value: string | number;
  icon: JSX.Element;
  highlight?: boolean;
};

export interface NewReportPayload {
  [REPORT_CONSTANTS.new_report.report_type]: string;
  [REPORT_CONSTANTS.new_report.report_input_value]: string;
  [REPORT_CONSTANTS.new_report.snap_request_id]?: string;
  [REPORT_CONSTANTS.new_report.report_input_encrypted]?: EncryptedData;
}

export type Ngrams = {
  [key: string]: string[];
};

export interface NgramsPayload {
  [REPORT_CONSTANTS.new_report.ngrams]: Ngrams;
};

export interface ReportData {
  [REPORT_CONSTANTS.new_report.report_id]: string;
  [REPORT_CONSTANTS.new_report.report_status]:
  | ReportStatusProps
  | EncryptedData;
  [REPORT_CONSTANTS.new_report.report_type]: string | EncryptedData;
  [REPORT_CONSTANTS.new_report.report_search_args]: EncryptedData;
  [REPORT_CONSTANTS.new_report.report_name]?: EncryptedData;
  [REPORT_CONSTANTS.new_report.report_omitted_nodes]?: EncryptedData;
  [REPORT_CONSTANTS.new_report.subject_name]?: EncryptedData;
  [REPORT_CONSTANTS.new_report.subject_mother_name]?: EncryptedData;
  [REPORT_CONSTANTS.new_report.subject_age]?: string | EncryptedData;
  [REPORT_CONSTANTS.new_report.subject_sex]?: EncryptedData;
  [REPORT_CONSTANTS.new_report.creation_at]?: string | EncryptedData;
  [REPORT_CONSTANTS.new_report.modified_at]?: string | EncryptedData;
  [REPORT_CONSTANTS.new_report.data]?: EncryptedData;
  [REPORT_CONSTANTS.new_report.report_id]?: EncryptedData;
  [REPORT_CONSTANTS.new_report.hmac]?: NgramsPayload;
  [REPORT_CONSTANTS.new_report.parent_folder_id]?: string | null;
  [REPORT_CONSTANTS.new_report.folder_name]?: string | null;
}

export type ReportSectionData = {
  [key: string]: ReportSection;
};

export interface ReportSection {
  title: string;
  subtitle: string;
  subsection: string;
  source: string[];
  data_count: number;
  is_deleted?: boolean;
  data: Array<Record<string, any>>;
}

export interface ReportMetadata {
  [REPORT_CONSTANTS.new_report.report_id]: string;
  [REPORT_CONSTANTS.new_report.report_status]: string;
  [REPORT_CONSTANTS.new_report.report_type]: string;
  [REPORT_CONSTANTS.new_report.report_search_args]: object;
  [REPORT_CONSTANTS.new_report.report_name]: string;
  [REPORT_CONSTANTS.new_report.creation_at]: string;
  [REPORT_CONSTANTS.new_report.modified_at]: string;
  /* podem vir sem valor */
  [REPORT_CONSTANTS.new_report.subject_name]: string;
  [REPORT_CONSTANTS.new_report.subject_mother_name]: string;
  [REPORT_CONSTANTS.new_report.subject_age]: number | null;
  [REPORT_CONSTANTS.new_report.subject_sex]: string;
}

export interface BreadcrumbItem {
  title: string;
  icon?: React.ReactNode;
  href?: string;
  onClick?: () => void;
}

export interface OutletContextType {
  setBreadcrumbs: React.Dispatch<React.SetStateAction<BreadcrumbItem[]>>;
}

/* USER CONFIG E INVITES */
export interface UserInviteRequest {
  email_invited: string;
  credits_sent: string;
  report_types: string[];
  type_invite: string;
}

export interface UserInviteResponse {
  user_id?: string,
  invite_id?: string,
  status_invite?: string,
  user_sender_id?: string,
  name_sender?: string,
  email_sender?: string,
  organization_name?: string,
  type_invite?: string,
  sent_at?: string,
  email_invited?: string,
  credits_sent?: string,
  report_types?: string[],
  name?: string,
}

export interface UserInvitesFilters {
  status_invite?: string;
  type_invite?: string;
  specific_date?: string;
  order?: string;
  column_order?: string;
  limit?: number;
  page?: number;
  search_email?: string;
}

export interface UserLogsFilters {
  is_user?: boolean;
  column_order?: string;
  order?: string;
  report_type?: string;
  limit?: number;
  page?: number;
  created_start_at?: string;
  created_end_at?: string;
}

export interface UserLogEntry {
  created_at: string,
  user_id: string,
  report_type: string,
  user_reports_id: string,
  name: string,
  role: string,
  email: string
}

export interface PaginationMetadata {
  current_page: number;
  total_pages: number;
  total_items: number;
  page_size: number;
  has_next: boolean;
  has_previous: boolean;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination?: PaginationMetadata;
}

export interface InviteAnswer {
  accept_invite: boolean;
  invite_id: string;
}

export type UserFromInviteData = UserData;

export interface ApiKeyRequest {
  apiKey: string;
}

export interface ApiKeyResponse {
  success: boolean;
  message: string;
}

export type StatusInvite = "enviado" | "negado" | "aceito" | "cancelado";

export interface FolderPayload {
  [REPORT_CONSTANTS.new_folder.folder_id]?: string;
  [REPORT_CONSTANTS.new_folder.folder_name]: EncryptedPayload;
  [REPORT_CONSTANTS.new_folder.hmac_folder_name]?: string[];
  [REPORT_CONSTANTS.new_folder.parent_folder_id]?: string | null;
  [REPORT_CONSTANTS.new_folder.user_reports_id_list]?: string[];
}

export interface MoveFilesPayload {
  [REPORT_CONSTANTS.move_files.src_folder_id]?: string | null;
  [REPORT_CONSTANTS.move_files.dest_folder_id]?: string | null;
  [REPORT_CONSTANTS.move_files.user_reports_id_list]?: string[];
  [REPORT_CONSTANTS.move_files.folder_id_list]?: string[];
}

export interface MergeFoldersPayload {
  [REPORT_CONSTANTS.merge_folders.folder_id]: string;
  [REPORT_CONSTANTS.merge_folders.folder_id_to_merge]: string;
}

export interface ReportPDFProps {
  sections: ReportSection[];
  metadata: ReportMetadata;
  profile_image?: string;
  organization_logo?: string;
  should_print_snap_logo?: boolean;
}

export interface EulaResponse {
  content: string;
  version: string;
  date: string;
}

export interface RenameReportPayload {
  [REPORT_CONSTANTS.new_report.report_id]: string;
  [REPORT_CONSTANTS.new_report.report_name]: EncryptedPayload;
  [REPORT_CONSTANTS.new_report.hmac]: NgramsPayload;
}