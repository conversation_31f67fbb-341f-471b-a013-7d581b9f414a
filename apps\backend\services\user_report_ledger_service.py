import logging
from datetime import datetime, timezone
import pytz
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy.exc import NoResultFound
from models.user_report_ledger_model import UserReportLedger
from schemas.user_report_ledger_schema import UserReportLedgerSchema
from services.base_service import BaseService

logger = logging.getLogger(__name__)

class UserReportLedgerService(BaseService):
    def __init__(self, db: AsyncSession, user_id: str):
        super().__init__(db)
        self.user_id=user_id
        logger.info("[UserReportLedgerService][__init__] Service initialized with db session: %s", db)

    async def create(self, log_data: UserReportLedgerSchema) -> UserReportLedger:
        logger.info("[UserReportLedgerService][create] Creating user_report_ledger with data: %s", log_data.model_dump())
        new_log = UserReportLedger(**log_data.model_dump())
        self.db.add(new_log)
        await self.db.commit()
        await self.db.refresh(new_log)
        logger.info("[UserReportLedgerService][create] Created user_report_ledger with ID: %s", new_log.user_reports_id)
        return new_log.user_reports_id

    async def update(self, user_reports_id, status: str, updated_at: datetime) -> UserReportLedger:

        logger.info("[UserReportLedgerService][update] Updating user_report_ledger with ID: %s and status: %s", user_reports_id, status)
        q = await self.db.execute(select(UserReportLedger).where(UserReportLedger.user_reports_id == user_reports_id))
        log = q.scalar_one_or_none()
        if not log:
            logger.info("[UserReportLedgerService][update] No user_report_ledger found for ID: %s", user_reports_id)
            raise NoResultFound(f"UserReportLedger with id {user_reports_id} not found")

        log.status_final = status
        log.updated_at = updated_at

        await self.db.commit()
        await self.db.refresh(log)
        logger.info("[UserReportLedgerService][update] Updated user_report_ledger with ID: %s", user_reports_id)
        return log 