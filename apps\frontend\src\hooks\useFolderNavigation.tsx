import { useQueryClient } from "@tanstack/react-query";
import { useNavigate, useLocation } from "react-router";
import { useReportListActions } from "~/store/reportListStore";
import { useBreadcrumbsActions } from "~/store/breadcrumbsStore";

export const useFolderNavigation = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const queryClient = useQueryClient();
  const { resetPagination, clearFolderPath } = useReportListActions();
  const { clearItems, setDirectNavigationMode } = useBreadcrumbsActions();

  const navigateToFolder = (folderId: string) => {
    resetPagination();
    const isDirect = !location.key || location.key === "default";
    setDirectNavigationMode(isDirect);

    navigate(`/pasta/${folderId}`);
  };

  const navigateToRoot = async () => {
    await resetPagination();
    clearFolderPath();
    clearItems();
    queryClient.resetQueries({
      queryKey: ["reports", "list", null],
      exact: true,
    });
    navigate("/");
  };

  const invalidateCurrentFolder = (folderId?: string | null) => {
    queryClient.invalidateQueries({
      queryKey: ["reports", "list", folderId],
      exact: true,
    });
  };

  return {
    navigateToFolder,
    navigateToRoot,
    invalidateCurrentFolder,
  };
};