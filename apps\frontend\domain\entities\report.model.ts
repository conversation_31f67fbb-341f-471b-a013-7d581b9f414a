import { REPORT_CONSTANTS } from "~/helpers/constants";
import type { ReportData, ListItemProps } from "~/types/global";
import { isObject, isEmptyObject, getTypeIcon, formatIsoDate, getAgeFromBirthDate } from "~/helpers";
import { BaseModel, BaseItemData, ItemType, DisplayableItem } from './base.model';

/**
 * A simplified, normalized representation of a report for the UI.
 */
export interface NormalizedReport extends BaseItemData {
  reportType: string;
  status: "success" | "pending" | "error";
  searchArgs: Record<string, unknown>;
  subjectName: string;
  subjectMotherName?: string;
  subjectAge?: string | number;
  subjectSex?: string;
  requestDate: string;
  lastModified: string;
  subjectPersonCount?: number;
  subjectCompanyCount?: number;
  type: ItemType.REPORT;
  parentFolderId: string | null;
  folderName?: string;
}

/**
 * Wrapper around raw ReportData, exposing easy-to-use getters
 */
export class ReportModel implements BaseModel<NormalizedReport>, DisplayableItem {
  readonly type = ItemType.REPORT;
  
  constructor(public readonly raw: ReportData) { }

  private R = REPORT_CONSTANTS.new_report;

  /** Unique identifier */
  get id(): string {
    const val = this.raw[this.R.report_id];
    if (typeof val !== "string") throw new Error("Invalid report ID");
    return val;
  }

  /** Display name */
  get name(): string {
    const val = this.raw[this.R.report_name];
    return typeof val === "string" ? val : "Untitled";
  }

  /** Report type key */
  get reportType(): string {
    const val = this.raw[this.R.report_type];
    return typeof val === "string" ? val : "unknown";
  }

  get subjectPersonCount(): number | undefined {
    const val = this.raw[this.R.subject_person_count];
    return typeof val === "number" ? val : undefined;
  }

  get subjectCompanyCount(): number | undefined {
    const val = this.raw[this.R.subject_company_count];
    return typeof val === "number" ? val : undefined;
  }

  /** One of the three status literals */
  get status(): NormalizedReport["status"] {
    const obj = this.raw[this.R.report_status] as any;
    const key = this.R.status_report;
    if (!obj || typeof obj !== "object" || !(key in obj)) {
      return "error";
    }
    const val = obj[key];
    if (
      val === REPORT_CONSTANTS.status.completed ||
      val === REPORT_CONSTANTS.status.pending ||
      val === REPORT_CONSTANTS.status.error
    ) {
      return val;
    }
    return "error";
  }

  /** Raw search arguments */
  get searchArgs(): Record<string, unknown> {
    const val = this.raw[this.R.report_search_args];
    return isObject(val) ? val : {};
  }

  /** Formatted items for the searchArgs section */
  get searchItems(): ListItemProps[] {
    if (!isObject(this.searchArgs) || isEmptyObject(this.searchArgs)) {
      return [];
    }
    return Object.entries(this.searchArgs).flatMap(([key, vals]) => {
      const arr = Array.isArray(vals) ? vals : [vals];
      return arr
        .filter((v) => v != null)
        .map((v) => ({
          label: key.toUpperCase(),
          value: String(v),
          icon: getTypeIcon(key),
          highlight: true,
        }));
    });
  }

  /** Subject full name */
  get subjectName(): string {
    const val = this.raw[this.R.subject_name];
    return typeof val === "string" ? val : "No name";
  }

  /** Subject's mother name (optional) */
  get subjectMotherName(): string | undefined {
    const val = this.raw[this.R.subject_mother_name];
    return typeof val === "string" && val.trim() ? val : undefined;
  }

  /** Subject age (optional) */
  get subjectAge(): string | number | undefined {
    const val = this.raw[this.R.subject_age];
    const reportType = this.raw[this.R.report_type];
    const subjectName = this.raw[this.R.subject_name];
    const subjectSex = this.raw[this.R.subject_sex];
    const subjectMotherName = this.raw[this.R.subject_mother_name];

    if (!val) return undefined;

    if (typeof subjectName === "string" && subjectName === REPORT_CONSTANTS.multiplos_registros_encontrados) {
      return undefined;
    }

    if (typeof val === "number") {
      return val;
    }

    if (typeof val === "string") {

      if (reportType === REPORT_CONSTANTS.types.cpf) {
        return getAgeFromBirthDate(val);
      }

      if (reportType === REPORT_CONSTANTS.types.cnpj) {
        return formatIsoDate(val, true);
      }

      // Telefone e email pode ser pessoa ou empresa
      if (reportType === REPORT_CONSTANTS.types.telefone || reportType === REPORT_CONSTANTS.types.email) {
        const hasSex = typeof subjectSex === "string" && subjectSex.trim();
        const hasMotherName = typeof subjectMotherName === "string" && subjectMotherName.trim();

        if (hasSex || hasMotherName) {
          return getAgeFromBirthDate(val);
        } else {
          // Caso não tenha sexo e mãe, é empresa
          return formatIsoDate(val, true);
        }
      }

      return getAgeFromBirthDate(val);
    }

    return undefined;
  }

  /** Subject sex (optional) */
  get subjectSex(): string | undefined {
    const val = this.raw[this.R.subject_sex];
    return typeof val === "string" && val.trim() ? val : undefined;
  }

  /** Timestamp when request was made */
  get requestDate(): string {
    const val = this.raw[this.R.creation_at];
    return typeof val === "string" ? formatIsoDate(val) : formatIsoDate(new Date().toISOString());
  }

  /** Timestamp when last modified */
  get lastModified(): string {
    const val = this.raw[this.R.modified_at];
    return typeof val === "string" ? formatIsoDate(val) : formatIsoDate(new Date().toISOString());
  }

  /** True if report is still generating */
  get isPending(): boolean {
    return this.status === REPORT_CONSTANTS.status.pending;
  }

  /** True if report generation errored */
  get isError(): boolean {
    return this.status === REPORT_CONSTANTS.status.error;
  }

  get createdAt(): string {
    return this.requestDate;
  }

  get modifiedAt(): string {
    return this.lastModified;
  }

  get parentFolderId(): string | null {
    const val = this.raw[this.R.parent_folder_id];
    return typeof val === "string" ? val : null;
  }

  get folderName(): string | undefined {
    const val = this.raw[this.R.folder_name];
    return typeof val === "string" ? val : undefined;
  }

  /** Bundle normalized data for easy UI use */
  toJSON(): NormalizedReport {
    return {
      id: this.id,
      name: this.name,
      reportType: this.reportType,
      status: this.status,
      searchArgs: this.searchArgs,
      subjectName: this.subjectName,
      subjectMotherName: this.subjectMotherName,
      subjectAge: this.subjectAge,
      subjectSex: this.subjectSex,
      requestDate: this.requestDate,
      lastModified: this.lastModified,
      subjectPersonCount: this.subjectPersonCount,
      subjectCompanyCount: this.subjectCompanyCount,
      createdAt: this.createdAt,
      modifiedAt: this.modifiedAt,
      type: ItemType.REPORT,
      parentFolderId: this.parentFolderId,
      folderName: this.folderName
    };
  }
}
