from sqlalchemy import Column, Text, ForeignKey, PrimaryKeyConstraint, Index
from sqlalchemy.dialects.postgresql import UUID as PostgresUUID

from models.base import Base


class FolderHmac(Base):
    __tablename__ = 'folder_name_hmac'
    __table_args__ = (
        PrimaryKeyConstraint("hmac", "folder_id"),
        Index("idx_folder_name_hmac_folder_id", "hmac"),
        {"schema": "public"}
    )

    hmac = Column(Text, nullable=False)
    folder_id = Column(PostgresUUID, ForeignKey("public.folders.folder_id"), nullable=False, index=True)

    def __repr__(self):
        return f"<FolderHmac(hmac={self.hmac}, folder_id={self.folder_id})>"
