from typing import Dict

from .BaseReportProcessor import BaseReportProcessor, ReportTypeConfig
from ..FormatFrontFormat import VinculoSection
from ..EntityExtractor import VinculoConfig


class PhoneReportProcessor(BaseReportProcessor):
    """Processor for Phone reports"""

    def get_config(self) -> ReportTypeConfig:
        return ReportTypeConfig(
            do_filter_doc='Telefone',
            do_filter_name='Telefone',
            title_format='Telefone',
            enabled_sections=[
                VinculoSection.POSSIVEIS_PESSOAS_RELACIONADAS, VinculoSection.POSSIVEIS_EMPRESAS_RELACIONADAS,
                VinculoSection.PHONES, VinculoSection.EMAILS, VinculoSection.ENDERECOS,
                VinculoSection.PARENTES, VinculoSection.OUTROS_CONTATOS, VinculoSection.IMAGENS, VinculoSection.NOMES_USUARIO,
                VinculoSection.OUTRAS_URLS, VinculoSection.REDES_SOCIAIS

            ]
        )

    def get_custom_vinculos(self, other_result_data: Dict, entity_type: str, search_value: str) -> Dict[
        str, VinculoConfig]:
        # Phone reports might have simpler vinculo requirements
        return {}

    def adjust_metadata(self, processed_result, metadata):
        self._adjust_metadata_age(processed_result, metadata)
        self._adjust_metadata_for_multiple_results(processed_result, metadata)