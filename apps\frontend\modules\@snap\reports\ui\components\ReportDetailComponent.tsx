import React, { useMemo } from 'react';
import { useNewStrategyMap } from '../strategies/NEW_ReportStrategyFactory';
import { useVisibleReportSections, useReportMode, useIsSaving, useIsTrashEnabled } from '../../context/ReportContext';
import { useSectionScrollToTop } from '../../hooks/useSectionScrollToTop';
import { useAccordionState } from '../../hooks/useAccordionState';
import { filterValidSections } from '../../helpers';
import ReportAccordionItem from './ReportAccordionItem';

const NEW_ReportDetailComponent: React.FC = () => {
  const mode = useReportMode();
  const isTrashEnabled = useIsTrashEnabled();
  const isTrashMode = mode === 'trash';
  const sections = useVisibleReportSections();
  const newMap = useNewStrategyMap();
  const isSaving = useIsSaving();
  const { visibleButtons, sectionRefs, handleScrollToTop } = useSectionScrollToTop(
    isTrashMode ? 'report-content-trash' : 'report-content'
  );
  const { toggleSection, isSectionOpen } = useAccordionState();
  const triggerClassName = "bg-accordion-header px-0 py-0 mb-4 data-[state=open]:mb-0 no-underline [&_div:first-child]:border-neutral-100 cursor-pointer";

  const validSections = useMemo(() => {
    return filterValidSections(sections, newMap);
  }, [sections, newMap]);

  const renderAccordionItem = useMemo(() => {
    return (section: typeof validSections[0], idx: number) => {
      const strategy = newMap[section.title];
      const sectionId = `section-${idx}`;
      const isOpen = isSectionOpen(sectionId);

      return (
        <ReportAccordionItem
          key={`${section.title}-${idx}`}
          section={section}
          idx={idx}
          isTrashMode={isTrashMode}
          isTrashEnabled={isTrashEnabled}
          isSaving={isSaving}
          strategy={strategy}
          isOpen={isOpen}
          onToggle={toggleSection}
          sectionRefs={sectionRefs}
          visibleButtons={visibleButtons}
          onScrollToTop={handleScrollToTop}
          triggerClassName={triggerClassName}
        />
      );
    };
  }, [
    newMap,
    isTrashMode,
    isTrashEnabled,
    isSaving,
    isSectionOpen,
    toggleSection,
    sectionRefs,
    visibleButtons,
    handleScrollToTop,
    triggerClassName
  ]);

  const renderSections = useMemo(() => {
    return (
      <div className="flex flex-col">
        {validSections.map((section, index) => renderAccordionItem(section, index))}
      </div>
    );
  }, [
    validSections,
    renderAccordionItem
  ]);

  return renderSections;
};

export default NEW_ReportDetailComponent;