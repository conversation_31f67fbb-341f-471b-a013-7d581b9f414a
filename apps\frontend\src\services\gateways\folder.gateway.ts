import { FolderPayload, MoveFilesPayload, MergeFoldersPayload, ReportData } from "~/types/global";
import { REPORTS_CLIENT } from "../clients/reports.client";

export interface FolderApiResponse {
  data: {
    folder_id: string;
    folder_name: {
      iv: string;
      encrypted: string;
    } | string;
    created_at: string;
    modified_at: string;
    parent_folder_id?: string | null;
    folder_path?: string;
    depth_level?: number;
  }[];
  pagination: {
    current_page: number;
    total_pages: number;
    total_items: number;
    page_size: number;
    has_next: boolean;
    has_previous: boolean;
  };
}

// TODO - criar domain/http/dto/create.folder.output.ts CreateFolderOutputDTO
export const createFolder = async (payload: FolderPayload) => {
  try {
    const response = await REPORTS_CLIENT.post(`/create_folder`, payload);
    return response.data;
  } catch (error) {
    console.error("createFolder Error creating folder:", error);
    throw error;
  }
};

export const deleteFolder = async (folderId: string) => {
  try {
    const response = await REPORTS_CLIENT.delete(`/delete_folder/${folderId}`);
    return response.data;
  } catch (error) {
    console.error("Error deleting folder:", error);
    throw error;
  }
};

export const renameFolder = async (payload: FolderPayload) => {
  try {
    const response = await REPORTS_CLIENT.patch(`/rename_folder`, payload);
    return response.data;
  } catch (error) {
    console.error("Error renaming folder:", error);
    throw error;
  }
};

export const moveFilesBetweenFolders = async (payload: MoveFilesPayload) => {
  try {
    const response = await REPORTS_CLIENT.put(`/move_file_between_folders`, payload);
    return response.data;
  } catch (error) {
    console.error("Error moving files between folders:", error);
    throw error;
  }
};

export const moveFolderToFolder = async (payload: MoveFilesPayload) => {
  try {
    const response = await REPORTS_CLIENT.put(`/move_file_between_folders`, payload);
    return response.data;
  } catch (error) {
    console.error("Error moving folder:", error);
    throw error;
  }
};

export const mergeFolders = async (payload: MergeFoldersPayload) => {
  try {
    const response = await REPORTS_CLIENT.put(`/merge_folders`, payload);
    return response.data;
  } catch (error) {
    console.error("Error merging folders:", error);
    throw error;
  }
};

export const getFolders = async (payload: {
  folder_id?: string;
  hmac_folder_name?: string;
  column_order?: string;
  order?: string;
  limit?: number;
  page?: number;
}): Promise<FolderApiResponse> => {
  try {
    const response = await REPORTS_CLIENT.get(`/get_folder_except_actual`, { params: payload });
    return response.data as FolderApiResponse;
  } catch (error) {
    console.error("Error getting folders:", error);
    throw error;
  }
};

export const getFoldersExceptContains = async (payload: {
  folder_id?: string;
  hmac_folder_name?: string;
  column_order?: string;
  order?: string;
  limit?: number;
  page?: number;
}): Promise<FolderApiResponse> => {
  try {
    const response = await REPORTS_CLIENT.get(`/get_folders_except_contains_folderid`, { params: payload });
    return response.data as FolderApiResponse;
  } catch (error) {
    console.error("Error getting folders except contains:", error);
    throw error;
  }
};
