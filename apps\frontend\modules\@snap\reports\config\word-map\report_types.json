{"status": {"completed": "success", "pending": "pending", "error": "error"}, "types": {"cpf": "cpf", "cnpj": "cnpj", "email": "email", "telefone": "telefone", "relacoes": "relacoes"}, "report_status_message": {"status": "status_code", "id": "id", "result": "result"}, "new_report": {"report_name": "report_name", "report_omitted_nodes": "omitted_notes", "report_type": "report_type", "report_status": "report_status", "report_search_args": "report_search_args", "subject_name": "subject_name", "subject_mother_name": "subject_mother_name", "subject_age": "subject_age", "subject_sex": "subject_sex", "creation_at": "created_at", "modified_at": "modified_at", "data": "data", "report_id": "user_reports_id", "status_report": "status_report", "report_input_encrypted": "report_input_encrypted", "snap_request_id": "snap_request_id", "hmac": "hmac", "ngrams": "n-grams", "organization_id": "organization_id", "user_id": "user_id", "subject_person_count": "subject_person_count", "subject_company_count": "subject_company_count", "parent_folder_id": "folder_id", "folder_name": "folder_name"}, "user_data": {"user_id": "user_id", "image": "image", "name": "name", "credits": "credits", "email": "email", "last_login": "last_login", "saved_reports": "saved_reports", "report_types": "report_types", "salt": "salt", "verifier": "verifier"}, "report_types_input": {"cpf": "cpf", "cnpj": "cnpj", "email": "email", "telefone": "telefone", "relacoes": "relacoes"}, "report_result_key": "data", "report_list_params": {"hmac_filter": "hmac_filter", "hmac_column": "hmac_column", "column_order": "column_order"}, "multiplos_registros_encontrados": "Multiplos registros encontrados", "new_folder": {"folder_id": "folder_id", "folder_name": "folder_name", "parent_folder_id": "parent_folder_id", "user_reports_id_list": "user_reports_id_list", "hmac_folder_name": "hmac_folder_name", "number_of_items": "number_of_items"}}