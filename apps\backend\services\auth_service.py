import logging
import asyncio
import time
logger = logging.getLogger(__name__)

from urllib.parse import quote
from fastapi import Request, Response
import httpx

from core.jwt_utils import verify_jwt, refresh_access_token
from core.config import settings
from exceptions.business_exceptions import User<PERSON><PERSON><PERSON><PERSON>enticated<PERSON>rror, FailRefreshTokenError, TokenExchangeFailedError
from core.jwt_utils import TokenVerificationFailedError

_refresh_locks = {}
_refresh_cache = {}


def _get_refresh_lock(key: str) -> asyncio.Lock:
    lock = _refresh_locks.get(key)
    if lock is None:
        lock = asyncio.Lock()
        _refresh_locks[key] = lock
    return lock


def _get_cached_tokens(old_refresh_token: str):
    entry = _refresh_cache.get(old_refresh_token)
    if not entry:
        return None
    access_token, refresh_token, expire_at = entry
    if expire_at <= time.time():
        try:
            del _refresh_cache[old_refresh_token]
        except Exception:
            pass
        return None
    return access_token, refresh_token


def _set_cached_tokens(old_refresh_token: str, new_access_token: str, new_refresh_token: str, ttl_seconds: int = 30):
    _refresh_cache[old_refresh_token] = (
        new_access_token,
        new_refresh_token,
        time.time() + ttl_seconds,
    )


async def auth_guard(request: Request, response: Response) -> dict:
    logger.info("[auth_guard] Checking tokens in cookies...")
    access_token = request.cookies.get("access_token")
    refresh_token = request.cookies.get("refresh_token")

    # Prefer tokens refreshed in this same request cycle
    tokens = getattr(request.state, "refreshed_tokens", None)
    logger.info("[auth_guard] Tokens from request.state: %s", tokens)
    if tokens:
        access_token = tokens.get("access_token") or access_token
        refresh_token = tokens.get("refresh_token") or refresh_token
        logger.info("[auth_guard] Using tokens from request.state for this request cycle.")

    if not access_token or not refresh_token:
        logger.error("[auth_guard] Missing access or refresh token.")
        raise UserNotAuthenticatedError()

    try:
        # Try to verify the JWT
        return await verify_jwt(access_token)
    except TokenVerificationFailedError as e:
        logger.error("[auth_guard] Token verification failed: %s", str(e))
        logger.info("[auth_guard] Token expired or invalid. Attempting to refresh...")
        lock = _get_refresh_lock(refresh_token)
        async with lock:
            # Use cached tokens if another request has refreshed already
            cached = _get_cached_tokens(refresh_token)
            if cached:
                new_access_token, new_refresh_token = cached
                logger.info(
                    "[auth_guard] Using cached refreshed tokens. access.len=%s, refresh.len=%s",
                    len(new_access_token) if new_access_token else 0,
                    len(new_refresh_token) if new_refresh_token else 0,
                )
            else:
                try:
                    tokens = getattr(request.state, "refreshed_tokens", None)
                    if tokens:
                        access_token = tokens.get("access_token") or access_token
                        refresh_token = tokens.get("refresh_token") or refresh_token
                    new_access_token, new_refresh_token = await refresh_access_token(refresh_token)
                    # Stash tokens for middleware to set on custom responses
                    try:
                        request.state.refreshed_tokens = {
                            "access_token": new_access_token,
                            "refresh_token": new_refresh_token,
                        }
                        logger.info("[auth_guard] Placed refreshed tokens into request.state for middleware sync.")
                    except Exception:
                        pass
                    logger.info(
                        "[auth_guard] Token refreshed successfully. Preparing to update cookies. access.len=%s, refresh.len=%s",
                        len(new_access_token) if new_access_token else 0,
                        len(new_refresh_token) if new_refresh_token else 0,
                    )
                    _set_cached_tokens(refresh_token, new_access_token, new_refresh_token)
                except Exception as refresh_error:
                    logger.error("[auth_guard] Refresh token process failed: %s", str(refresh_error))
                    logger.info("[auth_guard] Raising FailRefreshTokenError due to failed refresh or verification.")
                    raise FailRefreshTokenError()

        # Force cross-site compatible cookies during development
        is_secure = True
        same_site = "none"

        logger.info(
            "[auth_guard] Setting cookies on response. secure=%s samesite=%s path=/",
            is_secure,
            same_site,
        )

        # Update the dependency-injected response (works for standard responses)
        response.set_cookie(
            "access_token",
            new_access_token,
            httponly=True,
            secure=is_secure,
            samesite=same_site,
            path="/",
        )
        logger.info("[auth_guard] access_token cookie set on response headers.")
        response.set_cookie(
            "refresh_token",
            new_refresh_token,
            httponly=True,
            secure=is_secure,
            samesite=same_site,
            path="/",
        )
        try:
            # Count Set-Cookie headers without logging token contents
            set_cookie_headers = [
                h for (k, h) in ((k.decode().lower(), v.decode()) for k, v in getattr(response, "raw_headers", []))
                if k == "set-cookie"
            ]
            logger.info("[auth_guard] Set-Cookie headers count after setting: %s", len(set_cookie_headers))
        except Exception as header_err:
            logger.warning("[auth_guard] Could not count Set-Cookie headers: %s", header_err)



        logger.info("[auth_guard] Verifying new access token after refresh...")
        return await verify_jwt(new_access_token)



async def exchange_code_for_tokens(code: str, frontend):
    logger.info("[exchange_code_for_tokens] Received code: %s", code)
    token_endpoint = "%s/realms/%s/protocol/openid-connect/token" % (settings.KEYCLOAK_URL, settings.REALM_NAME)
    logger.info("[exchange_code_for_tokens] Token endpoint URL: %s", token_endpoint)

    logger.info("[exchange_code_for_tokens] Received frontend: %s", frontend)
    # frontend="http://localhost"
    redirect_uri = f"{settings.REDIRECT_URI_KEYCLOAK}?frontend={quote(frontend)}"
    # redirect_uri = f"{settings.REDIRECT_URI_KEYCLOAK}"
    data = {
        "grant_type": "authorization_code",
        "code": code,
        "redirect_uri": redirect_uri,
        "client_id": settings.CLIENT_ID_KEYCLOAK,
        "client_secret": settings.CLIENT_SECRET_KEYCLOAK,
    }

    max_attempts = 3
    for attempt in range(1, max_attempts + 1):
        try:
            async with httpx.AsyncClient(verify=settings.KEYCLOAK_VERIFY_SSL, timeout=10.0) as client:
                logger.info("[exchange_code_for_tokens] Sending POST request to Keycloak... (attempt %s/%s)", attempt, max_attempts)
                response = await client.post(
                    token_endpoint,
                    data=data,
                    headers={"Content-Type": "application/x-www-form-urlencoded"}
                )
            break
        except httpx.RequestError as e:
            if attempt == max_attempts:
                logger.error("[exchange_code_for_tokens] Network error on last attempt: %s", e)
                raise TokenExchangeFailedError(0, str(e))
            backoff = 0.5 * (2 ** (attempt - 1))
            logger.warning("[exchange_code_for_tokens] Network error (attempt %s/%s): %s. Retrying in %.1fs", attempt, max_attempts, e, backoff)
            await asyncio.sleep(backoff)

    logger.info("[exchange_code_for_tokens] Response status code: %s", response.status_code)

    if response.status_code != 200:
        try:
            error_text = response.text
            logger.error("[exchange_code_for_tokens] Error response from Keycloak: %s", error_text)
        except Exception as e:
            logger.error("[exchange_code_for_tokens] Error reading response: %s", e)
        raise TokenExchangeFailedError(response.status_code, response.text)

    logger.info("[exchange_code_for_tokens] Successfully received tokens.")
    return response.json()

