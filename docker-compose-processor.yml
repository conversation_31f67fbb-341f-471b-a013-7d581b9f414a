services:
  processor:
    image: my-processor-image
    build:
      context: .
      dockerfile: apps/processor/Dockerfile
    container_name: ${SPARK_APP_CONTAINER_NAME}
    environment:
      KAFKA_CONTAINER_NAME: ${KAFKA_CONTAINER_NAME}
      KAFKA_EXTERNAL_NAME: ${KAFKA_EXTERNAL_NAME}
      KAFKA_INTERNAL_PORT: ${KAFKA_INTERNAL_PORT}
      KAFKA_EXTERNAL_PORT: ${KAFKA_EXTERNAL_PORT}
      SHOULD_USE_KAFKA_CONTAINER: ${SHOULD_USE_KAFKA_CONTAINER:-true}
      DB_USER: ${DB_USER}
      DB_PASS: ${DB_PASS}
      DB_NAME: ${DB_NAME}
      DB_HOST: ${DB_HOST}
      DB_PORT: ${DB_PORT}

    networks:
      - mystack-net

    deploy:
      resources:
        limits:
          cpus: "2"
          memory: "2G"
        reservations:
          cpus: "1"
          memory: "1G"
    restart: unless-stopped
