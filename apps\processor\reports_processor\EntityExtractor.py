from collections import defaultdict
from dataclasses import dataclass
from functools import lru_cache
from typing import Union, List, Dict, Any, Callable, Optional

from unidecode import unidecode

from reports_processor.constants import ReportKeys, empregos_vinculo, logger
from reports_processor.FormatFrontFormat import FinalFrontFormatBasic
from reports_processor.formatters.formatters import map_to_front_format, merge_entity_lists
from reports_processor.utils import compare_and_update_vinculo


@dataclass
class ExtractionResult:
    """Data class for extraction results"""
    data: Union[List[Dict], Dict[str, List[Dict]]]
    sources: List[str]

@dataclass
class VinculoConfig:
    """Configuration for vinculo extraction"""
    front_format: Optional[FinalFrontFormatBasic] = None
    extract_func: Optional[Callable] = None
    extract_type: Optional[str] = None
    filter_source_name: Optional[str] = None
    try_merge: bool = True
    skip_lists: bool = True
    item_callback: Optional[Callable] = None
    extra_data_callback: Optional[Callable] = None
    filter_base_data_callback: Optional[Callable] = None
    replace_data_processor: Optional[Callable] = None
    reverse: bool = False
    extract_type_starts_with: Optional[str] = None

class EntityExtractor:
    """Main class for entity extraction operations"""

    def __init__(self):
        self._extraction_cache = {}

    @staticmethod
    def filter_by_non_empty_indexes(source: List, *other_lists: List[List]) -> List:
        """Filter source list by non-empty indexes in other lists"""
        return [
            source[i]
            for i in range(len(source))
            if any(other_list[i] for other_list in other_lists)
        ]

    @lru_cache(maxsize=128)
    def _get_normalized_vinculo(self, vinculo: str) -> str:
        """Cache normalized vinculo strings for performance"""
        return unidecode(vinculo).lower() if vinculo else ""

    def extract_vinculos_campanha(
            self,
            data_dict: Dict[str, Dict],
            entity_type,
            search_value: str,
            config_obj: VinculoConfig
    ) -> ExtractionResult:
        """Extract campaign-related relationships"""

        reverse = config_obj.reverse
        filter_source_name = config_obj.filter_source_name

        # Configuration based on reverse flag
        config = self._get_campanha_config(reverse)

        base_data_enviada, base_data_recebida, sources = [], [], []

        # Collect data from all sources
        for source, base_dict in data_dict.items():
            sources.append(source)
            if filter_source_name and filter_source_name not in source:
                base_data_enviada.append([])
                base_data_recebida.append([])
                continue

            base_data_recebida.append(base_dict.get(config['extract_type_recebida'], []))
            base_data_enviada.append(base_dict.get(config['extract_type_enviada'], []))

        if not any(base_data_enviada + base_data_recebida):
            return ExtractionResult([], [])

        used_sources = self.filter_by_non_empty_indexes(sources, base_data_enviada, base_data_recebida)
        formatted_data = {}

        # Process enviada data
        self._process_campanha_data(
            base_data_enviada, sources, config['enviada_subtype'],
            config['enviadas_result'], formatted_data
        )

        # Process recebida data
        self._process_campanha_recebida_data(
            base_data_recebida, sources, config['recebida_subtypes'],
            config['recebidas_result'], formatted_data
        )

        return ExtractionResult(formatted_data, used_sources)

    def _get_campanha_config(self, reverse: bool) -> Dict[str, Any]:
        """Get configuration for campanha extraction"""
        base_config = {
            'enviada_subtype': ReportKeys.CANDIDATO,
            'recebida_subtypes': [ReportKeys.PESSOA, ReportKeys.EMPRESA],
            'extract_type_enviada': ReportKeys.PESSOA,
            'extract_type_recebida': ReportKeys.CANDIDATO,
        }

        if reverse:
            base_config.update({
                'enviadas_result': 'recebida',
                'recebidas_result': 'enviada'
            })
        else:
            base_config.update({
                'enviadas_result': 'enviada',
                'recebidas_result': 'recebida'
            })

        return base_config

    def _process_campanha_data(
            self,
            base_data: List[List],
            sources: List[str],
            subtype: str,
            result_key: str,
            formatted_data: Dict
    ):
        """Process campanha data for a specific type"""
        for idx, data in enumerate(base_data):
            base_front_data = [
                map_to_front_format(x, sources[idx], skip_lists=False)
                for item in data
                if subtype in item
                for x in item[subtype]
            ]
            formatted_data[result_key] = merge_entity_lists(
                formatted_data.get(result_key, []), base_front_data
            )

    def _process_campanha_recebida_data(
            self,
            base_data_recebida: List[List],
            sources: List[str],
            recebida_subtypes: List[str],
            result_key: str,
            formatted_data: Dict
    ):
        """Process recebida data for campanha"""
        for idx, data in enumerate(base_data_recebida):
            base_front_data = [
                map_to_front_format(x, sources[idx], skip_lists=False)
                for cur_entity in data
                for y in recebida_subtypes if y in cur_entity
                for x in cur_entity[y]
            ]
            formatted_data[result_key] = merge_entity_lists(
                formatted_data.get(result_key, []), base_front_data
            )

    def extract_vinculos_empregaticios(
            self,
            data_dict: Dict[str, Dict],
            entity_type,
            search_value: str,
            config_obj: VinculoConfig
    ) -> ExtractionResult:
        """Extract employment relationships"""

        sources, empresas, remuneracoes = [], [], []

        for source, base_dict in data_dict.items():
            empresas.append([
                x for x in base_dict.get(ReportKeys.EMPRESA, [])
                if compare_and_update_vinculo(x, empregos_vinculo)
            ])
            remuneracoes.append(base_dict.get(ReportKeys.REMUNERACAO, []))
            sources.append(source)

        if not any(empresas) and not any(remuneracoes):
            return ExtractionResult([], [])

        used_sources = self.filter_by_non_empty_indexes(sources, empresas, remuneracoes)
        formatted_remuneracoes, formatted_empresas = [], []

        for idx, source_data in enumerate(remuneracoes):
            processed_remuneracoes = self._process_remuneracoes_data(source_data, sources[idx])
            formatted_remuneracoes = merge_entity_lists(formatted_remuneracoes, processed_remuneracoes)

            base_empresa = []

            for em in empresas[idx]:
                vinculos = em.get(ReportKeys.VINCULO)

                if isinstance(vinculos, list) and all(isinstance(v, dict) for v in vinculos):
                    for vinculo in vinculos:
                        merged_em = {**em, **vinculo}  # shallow merge
                        base_empresa.append(
                            map_to_front_format(merged_em, sources[idx], skip_lists=True)
                        )
                else:
                    base_empresa.append(
                        map_to_front_format(em, sources[idx], skip_lists=True)
                    )

            formatted_empresas = merge_entity_lists(formatted_empresas, base_empresa)

        final_data = merge_entity_lists(formatted_empresas, formatted_remuneracoes)
        return ExtractionResult(final_data, used_sources)

    def _process_remuneracoes_data(self, source_data: List[Dict], source: str) -> List[Dict]:
        """Process remuneration data"""
        base_remuneracao = []
        for rm in source_data:
            if ReportKeys.EMPRESA not in rm or len(rm[ReportKeys.EMPRESA]) != 1:
                logger.warning(
                    f"[extract_vinculos_empregaticios] Invalid company data in remuneration: {rm}, skipping"
                )
                continue

            company_data = rm.pop(ReportKeys.EMPRESA)[0]
            rm.update(company_data)
            base_remuneracao.append(map_to_front_format(rm, source, skip_lists=True))

        return base_remuneracao

    def extract_vinculos_genericos(
            self,
            data_dict: Dict[str, Dict],
            entity_type,
            search_value: str,
            config_obj: VinculoConfig

    ) -> ExtractionResult:
        """Generic extraction function with improved type safety and performance"""

        base_data, sources = [], []

        # Collect data from sources
        for source, base_dict in data_dict.items():
            if config_obj.filter_source_name and config_obj.filter_source_name not in source:
                base_data.append([])
            elif config_obj.filter_base_data_callback and callable(config_obj.filter_base_data_callback):
                if config_obj.extract_type:
                    base_data.append(config_obj.filter_base_data_callback(base_dict.get(config_obj.extract_type, []), entity_type, search_value))
                if config_obj.extract_type_starts_with:
                    base_data.append([
                        config_obj.filter_base_data_callback({k: v}, entity_type, search_value) for k, v in base_dict.items()
                        if k.startswith(config_obj.extract_type_starts_with)])
            else:
                if config_obj.extract_type:
                    base_data.append(base_dict.get(config_obj.extract_type, []))
                if config_obj.extract_type_starts_with:
                    base_data.append([
                        v for k, v in base_dict.items()
                        if k.startswith(config_obj.extract_type_starts_with)
                    ])
            sources.append(source)

        if not any(base_data):
            return ExtractionResult([], [])

        used_sources = self.filter_by_non_empty_indexes(sources, base_data)
        formatted_data = []
        extra_formatted_data = []

        if config_obj.replace_data_processor is not None and callable(config_obj.replace_data_processor):
            formatted_data = config_obj.replace_data_processor(base_data, sources)

        else:
            for idx, data in enumerate(base_data):
                # Process extra data if callback provided
                if config_obj.extra_data_callback and callable(config_obj.extra_data_callback):
                    extra_data = defaultdict(list)
                    for d in data:
                        for vinculo, items in config_obj.extra_data_callback(d, entity_type, search_value).items():
                            for item in items:
                                if item not in extra_data[vinculo]:
                                    extra_data[vinculo].append(item)

                    if extra_data:
                        extra_data_front = map_to_front_format(extra_data, sources[idx], skip_lists=config_obj.skip_lists)
                        extra_formatted_data = merge_entity_lists(extra_formatted_data, extra_data_front)

                # Process main data
                base_front_data = []
                for d in data:
                    processed_item = config_obj.item_callback(d, entity_type, search_value) if config_obj.item_callback else d
                    if type(processed_item) is list:
                        for item in processed_item:
                            mapped = map_to_front_format(item, sources[idx], skip_lists=config_obj.skip_lists)
                            if mapped:
                                base_front_data.append(mapped)
                    else:
                        mapped = map_to_front_format(processed_item, sources[idx], skip_lists=config_obj.skip_lists)
                        if mapped:
                            base_front_data.append(mapped)

                if config_obj.try_merge:
                    formatted_data = merge_entity_lists(formatted_data, base_front_data)
                else:
                    formatted_data.extend(base_front_data)

            if extra_formatted_data:
                formatted_data = {'base': formatted_data, 'extra': extra_formatted_data}

        return ExtractionResult(formatted_data, used_sources)
