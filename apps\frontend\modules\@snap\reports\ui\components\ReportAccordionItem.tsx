import React, { memo, useMemo } from 'react';
import { Accordion } from './CustomAccordion';
import { Badge } from './base/badge';
import { ScrollToTopButton } from './ScrollToTopButton';
import { LiaTrashRestoreAltSolid, LiaTrashAltSolid } from "react-icons/lia";
import { Text } from '@snap/design-system';
import { createSectionKey } from '../../helpers';

interface ReportAccordionItemProps {
  section: {
    title: string;
    data: any;
    data_count: number;
    subsection?: any;
  };
  idx: number;
  isTrashMode: boolean;
  isTrashEnabled: boolean;
  isSaving: boolean;
  strategy: {
    render: (data: any) => React.ReactNode[];
    deleteSectionEntries?: () => void;
  };
  isOpen: boolean;
  onToggle: (sectionId: string) => void;
  sectionRefs: React.RefObject<Record<string | number, HTMLDivElement | null>>;
  visibleButtons: Record<string | number, boolean>;
  onScrollToTop: (sectionKey: string) => void;
  triggerClassName: string;
}

const ReportAccordionItem: React.FC<ReportAccordionItemProps> = ({
  section,
  idx,
  isTrashMode,
  isTrashEnabled,
  isSaving,
  strategy,
  isOpen,
  onToggle,
  sectionRefs,
  visibleButtons,
  onScrollToTop,
  triggerClassName
}) => {
  const sectionId = `section-${idx}`;
  const sectionKey = createSectionKey(section.title);

  // ESSENTIAL: Memoize rendered content to prevent re-execution on scroll
  const renderedContent = useMemo(() => {
    return strategy.render(section.data);
  }, [section.data, section.title]); // Only depend on data, not strategy object

  const handleDeleteSection = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (!isSaving && strategy.deleteSectionEntries) {
      strategy.deleteSectionEntries();
    }
  };

  const handleToggle = () => {
    onToggle(sectionId);
  };

  const handleScrollToTop = () => {
    onScrollToTop(sectionKey);
  };

  return (
    <div
      ref={(el) => {
        sectionRefs.current[sectionKey] = el;
      }}
      className="relative w-full"
    >
      <Accordion type="multiple" value={isOpen ? [sectionId] : []}>
        <Accordion.Item key={sectionId} value={sectionId} className='border-b-0'>
          <div className="group sticky top-0 z-10" onClick={handleToggle}>
            <Accordion.Trigger className={triggerClassName}>
              <div className="flex items-center gap-4 w-full justify-between pr-8">
                <div className="flex items-center gap-4">
                  <Text variant={isTrashMode ? "label-md" : "label-lg"} className="uppercase">
                    {section.title}
                  </Text>
                  {!isTrashMode && (
                    <Badge
                      className="rounded-2xl px-4 py-0.5 bg-accordion-badge hover:bg-accordion-badge border-0"
                    >
                      <Text className="text-foreground">{section.data_count}</Text>
                    </Badge>
                  )}
                </div>
                {strategy.deleteSectionEntries && isTrashEnabled && (
                  <span
                    onClick={handleDeleteSection}
                    title={isSaving ? "Salvando alterações..." : (isTrashMode ? "Restaurar seção" : "Deletar seção")}
                    className={`
                      flex items-center justify-center
                      w-10 h-10
                      opacity-0
                      group-hover:opacity-100
                      cursor-pointer
                      rounded-full
                      hover:bg-black/10
                      ${isSaving ? 'pointer-events-none cursor-wait' : ''}
                      transition-opacity duration-200
                    `}
                  >
                    {isTrashMode ? (
                      <LiaTrashRestoreAltSolid
                        size={32}
                        color={isSaving ? "var(--muted-foreground)" : "var(--foreground)"}
                      />
                    ) : (
                      <LiaTrashAltSolid
                        size={32}
                        color={isSaving ? "var(--muted-foreground)" : "var(--primary)"}
                      />
                    )}
                  </span>
                )}
              </div>
            </Accordion.Trigger>
          </div>
          <ScrollToTopButton
            isVisible={visibleButtons[sectionKey]}
            onScrollToTop={handleScrollToTop}
          />
          <Accordion.Content className="px-5">
            <div className="pt-5">
              {renderedContent.map((el: React.ReactNode, j: number) => (
                <div key={j}>{el}</div>
              ))}
            </div>
          </Accordion.Content>
        </Accordion.Item>
      </Accordion>
    </div>
  );
};

ReportAccordionItem.displayName = 'ReportAccordionItem';

export default ReportAccordionItem;
