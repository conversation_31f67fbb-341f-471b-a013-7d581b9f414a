from reports_processor.constants import ReportKeys, main_entity_fields, other_id_fields, secondary_fields, \
    Constants, logger, skip_fields, CacheableEntityTypes, FormattableEntity
from reports_processor.FormatFrontFormat import front_entity_map, FinalFrontFormatBasic, FinalFrontFormat
from reports_processor.dutils.EntityCache import EntityCache
from reports_processor.formatters.MainValuesFormatter import format_normalized_document
from reports_processor.utils import strip_trailing_digits, get_new_key, normalize_cep, \
    is_masked_document_match, get_constant, basic_property_dict
from unidecode import unidecode
from collections import deque



def merge_entity_lists(existing_list, new_entities, level=0):
    """
    Intelligently merges entity lists, identifying matching entities even with varied field names.

    This function handles:
    - Entities with different identifying fields (e.g., CPF, nome_completo)
    - Merging fields from matching entities without duplication
    - Preserving unique entities
    - Handling both direct values and structured values with {value, label, source} format
    - Comparing normalized keys (without trailing digits) to avoid duplicates

    Args:
        existing_list: Current list of entity dictionaries
        new_entities: New entity dictionaries to merge in

    Returns:
        Updated list with merged entities
    """
    # If either list is empty, return the other
    if not existing_list:
        return new_entities
    if not new_entities:
        return existing_list

    # Function to get the actual value for comparison
    def get_value(entity_field):
        if isinstance(entity_field, dict) and "value" in entity_field:
            return entity_field["value"]
        return entity_field

    # Function to find matching keys and values in a dictionary
    def find_matching_keys(base_key, value, entity_dict):
        """Find all keys in entity_dict that have the same base and matching value"""
        matching_keys = []
        normalized_key = strip_trailing_digits(base_key)

        for existing_key, existing_value in entity_dict.items():
            if strip_trailing_digits(existing_key) == normalized_key:
                if get_value(existing_value) == get_value(value):
                    matching_keys.append(existing_key)

        return matching_keys

    # Function to find disambiguous keys in a dictionary
    def disambiguous_keys(base_key, entity_dict):
        """Find all keys in entity_dict that have the same base and matching value"""

        normalized_key = strip_trailing_digits(base_key)
        if normalized_key in entity_dict:
            return get_new_key(base_key, entity_dict)
        return normalized_key

    # Function to determine if two entities represent the same real-world entity
    def entities_match(entity1, entity2):
        # Try to match on any identifier field
        for field in list(main_entity_fields.values()):
            if field in entity1 and field in entity2:
                value1 = get_value(entity1[field])
                value2 = get_value(entity2[field])
                if field == ReportKeys.CEP:
                    value1 = normalize_cep(value1)
                    value2 = normalize_cep(value2)

                if value1 == value2:
                    return True

        # Check for partial CPF/CNPJ matches with masking
        if (ReportKeys.CPF in entity1 and ReportKeys.CPF in entity2 and
                is_masked_document_match(get_value(entity1[ReportKeys.CPF]),
                                         get_value(entity2[ReportKeys.CPF]))):
            return True

        if (ReportKeys.CNPJ in entity1 and ReportKeys.CNPJ in entity2 and
                is_masked_document_match(get_value(entity1[ReportKeys.CNPJ]),
                                         get_value(entity2[ReportKeys.CNPJ]))):
            return True

        # Consider other string fields that might identify the same entity
        name_match = False
        for name_field in other_id_fields:
            if (name_field in entity1 and name_field in entity2 and
                    get_value(entity1[name_field]) == get_value(entity2[name_field])):
                name_match = True
                break

        # If names match, check for another matching field to confirm
        if name_match:
            for field in secondary_fields:
                if (field in entity1 and field in entity2 and
                        get_value(entity1[field]) == get_value(entity2[field])):
                    return True

        return False

    # Process each new entity
    for new_entity in new_entities:
        match_found = False

        # Try to find a match in existing entities
        for existing_entity in existing_list:
            if entities_match(existing_entity, new_entity):
                # Merge the entities by updating existing one with non-duplicative values
                for key, value in new_entity.items():
                    # Find all matching keys in the existing entity (with same base key and value)
                    matching_keys = find_matching_keys(key, value, existing_entity)

                    if not matching_keys:
                        # No matching fields - add as new field
                        normalized_key = strip_trailing_digits(key)
                        if normalized_key in existing_entity and isinstance(existing_entity[normalized_key], list):
                            if level == 0:
                                existing_entity[normalized_key] = merge_entity_lists(existing_entity[normalized_key], new_entity[key], level=level + 1)
                            else:
                                existing_entity[normalized_key].extend(value)

                        else:
                            new_key_field = disambiguous_keys(key, existing_entity)
                            existing_entity[new_key_field] = value
                    else:
                        # At least one matching field exists - merge if structured format
                        matching_key = matching_keys[0]  # Take the first match to merge with
                        existing_value = existing_entity[matching_key]

                        # Check if both are structured dictionaries with value, label, source
                        if (isinstance(value, dict) and "value" in value and
                                isinstance(existing_value, dict) and "value" in existing_value):

                            # Values already match (from our find_matching_keys check)
                            # So now we just need to merge sources if they exist
                            if "source" in value and "source" in existing_value:
                                if isinstance(value["source"], set) and isinstance(existing_value["source"], set):
                                    existing_value["source"].update(value["source"])
                                elif isinstance(value["source"], set):
                                    existing_value["source"].update(value["source"])
                                elif isinstance(existing_value["source"], set):
                                    existing_value["source"].add(value["source"])
                                else:
                                    existing_value["source"] = {existing_value["source"], value["source"]}

                        # Values match but format differs or they're not structured - keep existing
                        # No need to add a new field since values are the same

                match_found = True
                break

        # If no match, add the new entity to the list
        if not match_found:
            existing_list.append(new_entity)

    return existing_list # mae eh a mesma


def map_to_front_format(data_dict, source, skip_lists=True):
    result = {}

    for k, v in data_dict.items():
        # Skip lists if requested
        if skip_lists and isinstance(v, list):
            continue

        # Handle lists of dictionaries - each dict will be converted to front format
        if isinstance(v, list) and all(isinstance(item, dict) for item in v):
            transformed_list = []
            for item in v:
                # Transform each dictionary in the list to front format
                transformed_item = {
                    sub_k: basic_property_dict(sub_v, get_constant(Constants.PropToLabel, sub_k, sub_k), {source})
                    for sub_k, sub_v in item.items()
                    if not (skip_lists and isinstance(sub_v, list))
                }
                transformed_list.append(transformed_item)
            result[k] = transformed_list
        else:
            # Handle regular values
            result[k] = basic_property_dict(v, get_constant(Constants.PropToLabel, k, k), {source})

    return result


def organize_main_entity(entities, entity_type, search_type, search_value):
    """
    Identifies the main entity and organizes data around it.
    Returns a restructured entity dictionary.
    """
    result = {entity_type: {}}
    main_entity_indices = []

    if entity_type not in entities:
        return result

    # Find main entity instances
    for idx, entity in enumerate(entities[entity_type]):
        for key, value in entity.items():
            if key == search_type and is_masked_document_match(search_value, value):
                # Extract scalar fields for main entity
                main_entity_info = {k: v for k, v in entity.items() if isinstance(v, str)}

                # Extract nested entities
                nested_entities = {k: v for k, v in entity.items() if isinstance(v, list)}

                # Merge nested entities into main result
                for nest_key, nest_items in nested_entities.items():
                    if nest_key == entity_type:
                        entities[entity_type].extend(nest_items)
                    elif nest_key in entities:
                        entities[nest_key].extend(nest_items)
                    else:
                        entities[nest_key] = nest_items

                # Update with main entity info
                result[entity_type].update(main_entity_info)
                main_entity_indices.append(idx)
                break

    # Remove already processed main entities
    for idx in sorted(main_entity_indices, reverse=True):
        del entities[entity_type][idx]

    # Update with remaining organized entities

    type_priority = {str: 0, list: 1}

    result[entity_type].update(dict(sorted(
        entities.items(),
        key=lambda item: type_priority.get(type(item[1]), 2)
    )))

    return result

def extract_mapped_fields(entity, external_data_map, display_values):
    display_values = display_values or {}
    result = {}
    remaining = entity.copy()

    for output_field, possible_keys in external_data_map.items():
        for key in possible_keys:
            if key in remaining:
                value = remaining.pop(key)
                value["label"] = display_values.get(output_field, output_field)
                result[output_field] = value
                break
        else:
            logger.warning(f"[extract_mapped_fields] Could not find keys {possible_keys} for output field '{output_field}' in entity {entity.keys()}")

    for key in skip_fields:
        remaining.pop(key, None)

    return result, remaining


def format_and_decache_fields(entity_types, data):
    stack = deque()
    stack.append((entity_types, data))

    while stack:
        current_types, current_entities = stack.pop()

        for tpe in current_types:

            ent_type = None

            try:
                ent_type = CacheableEntityTypes(tpe)
            except ValueError:
                pass

            if ent_type is not None:
                prefix = ent_type.main_document

                for entity in current_entities:
                    formatted_doc = EntityCache.get_formatted_value_entity(ent_type, entity)
                    if not formatted_doc:
                        continue
                    for k in list(entity):
                        if k.startswith(prefix) and (k[len(prefix):].startswith('_') or k[len(prefix):][:1].isdigit()):
                            del entity[k]

                    entity[prefix]["value"], entity[prefix]["source"] = formatted_doc

            ent_type = None
            try:
                ent_type = FormattableEntity(tpe)
            except ValueError:
                pass

            if ent_type is not None:
                field = ent_type.main_document

                for entity in current_entities:
                    cur_doc = entity.get(field, None)
                    if not cur_doc:
                        continue

                    entity[field]["value"] = format_normalized_document(entity[field]["value"], field)

        for entity in current_entities:
            for k, v in entity.items():
                if isinstance(v, list):
                    stack.append(([k], v))

def final_front_format(metadata: FinalFrontFormatBasic, sources, data):

    base_data = {
        metadata.TITLE_FIELD: metadata.title,
        metadata.SUBTITLE_FIELD: metadata.subtitle,
        metadata.SUBSECTION_FIELD: metadata.subsection,
        metadata.IS_DELETED: False,
        metadata.SOURCES_FIELD: sources,
        metadata.DATA_FIELD: []
    }

    format_and_decache_fields(metadata.entity_type, data)
    base_data[metadata.DATA_COUNT_FIELD] = len(data) if metadata.count_level == 0 else len(data[0]) if len(data) == 1 else 0

    if isinstance(metadata, FinalFrontFormat):
        for entity in data:
            mapped, detalhes = extract_mapped_fields(entity, metadata.external_data_map, metadata.display_values)

            # Move list values from detalhes to mapped using front_entity_map
            keys_to_move = [k for k, v in detalhes.items() if isinstance(v, list)] + metadata.extra_data_to_move

            for key in keys_to_move:
                new_key = front_entity_map.get(key, key)
                if key not in detalhes:
                    key = unidecode(key).lower()
                if key not in detalhes:
                    continue
                tmp = detalhes.pop(key)

                if isinstance(tmp, list):
                    mapped[new_key] = [basic_property_dict(x, key, sources) for x in tmp]
                else:
                    mapped[new_key] = tmp

            mapped[metadata.DETALHES_FIELD] = detalhes
            base_data[metadata.DATA_FIELD].append(mapped)

    else:
        # FinalFrontFormatBasic fallback
        detalhes_list = [
            basic_property_dict(entity, metadata.title, sources)
            for entity in data
        ]
        base_data[metadata.DATA_FIELD].append({metadata.DETALHES_FIELD: detalhes_list})

    return base_data