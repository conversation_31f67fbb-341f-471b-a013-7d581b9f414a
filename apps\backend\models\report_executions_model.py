from sqlalchemy import Column, ForeignKey, DateTime, Text
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import UUID as PostgresUUID

from models.base import Base

class ReportExecutions(Base):
    __tablename__ = 'report_executions'
    __table_args__ = {"schema": "public"}

    report_executions_id = Column(PostgresUUID, primary_key=True, server_default=func.gen_random_uuid())
    user_reports_id = Column(PostgresUUID, ForeignKey("public.user_report_ledger.user_reports_id"), nullable=False, index=True)
    user_id = Column(PostgresUUID, ForeignKey("public.users.user_id"), nullable=False, index=True)

    # Date columns - all indexed except dt_deleted
    dt_inicio_tentativa = Column(DateTime(timezone=True), nullable=True, index=True)
    dt_aquisicao_snap = Column(DateTime(timezone=True), nullable=True, index=True)
    dt_chegada_minio_reports = Column(DateTime(timezone=True), nullable=True, index=True)
    dt_proc_spark = Column(DateTime(timezone=True), nullable=True, index=True)
    dt_chegada_minio_processed_reports = Column(DateTime(timezone=True), nullable=True, index=True)
    dt_envio_websocket = Column(DateTime(timezone=True), nullable=True, index=True)
    dt_salvo_pelo_frontend = Column(DateTime(timezone=True), nullable=True, index=True)
    dt_deleted = Column(DateTime(timezone=True), nullable=True)  # no index

    # Status and error columns
    status_tentativa = Column(Text, nullable=False, index=True)
    detalhe_erro = Column(Text, nullable=True)

    # Relationships
    user_report_ledger = relationship("UserReportLedger", back_populates="report_executions")

    def __repr__(self):
        return f"<ReportExecutions(report_executions_id={self.report_executions_id}, status_tentativa={self.status_tentativa})>"
