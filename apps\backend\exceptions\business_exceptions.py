# apps/backend/exceptions/business.py

from exceptions.base_exceptions import (
    ProblemDetail, ValidationError, AuthenticationError, AuthorizationError, NotFoundError, BusinessLogicError, InternalServerError
)

# ============================================================================
# REGIONS INDEX (Table of Contents)
# ============================================================================
# 1. Erros de Autorização
# 2. Erros de Autenticação
# 3. Erros de Validação
# 4. Erros de Não Encontrado
# 5. Erros de Detalhes do Problema
# 6. <PERSON><PERSON><PERSON> de Negócio
# 7. <PERSON><PERSON>s de Serviços Externos
# ============================================================================

#region ############## Erros de Autorização ###############


class ReportAccessDeniedError(AuthorizationError):
    def __init__(self, report_type: str):
        super().__init__(
            detail=f'Usuário não tem acesso ao relatório do tipo "{report_type}"!',
            report_type=report_type
        )


class InvitePermissionDeniedError(AuthorizationError):
    def __init__(self, action: str):
        super().__init__(detail=f"Usuário não tem permissão para {action}.")


class NotInSameOrganizationError(AuthorizationError):
    def __init__(self):
        super().__init__(detail="Usuário não está na mesma organização que o usuário alvo.")

class PrintSnapLogoPermissionDeniedError(AuthorizationError):
    def __init__(self):
        super().__init__(detail="Usuário não tem permissão para alterar a visualização do logo do SNAP.")


class ReportPermissionDeniedError(AuthorizationError):
    def __init__(self):
        super().__init__(detail="Usuário sem permissão para acessar o relatório.")


class ReportApiKeyMissingError(AuthorizationError):
    def __init__(self):
        super().__init__(detail="Usuário sem api_key para acessar o relatório.")


class ReportInsufficientCreditsError(AuthorizationError):
    def __init__(self):
        super().__init__(detail="Usuário sem créditos para acessar o relatório.")


class UserEditPermissionDeniedError(AuthorizationError):
    def __init__(self):
        super().__init__(detail="Usuário não tem permissão para editar outro usuário.")


#endregion



#region  ############# Erros de Autenticação ###############

class InvalidTokenError(AuthenticationError):
    def __init__(self):
        super().__init__(detail="Token inválido.")


class SessionExpiredError(AuthenticationError):
    def __init__(self):
        super().__init__(detail="Sessão expirada. Faça login novamente.")


class UserNotAuthenticatedError(AuthenticationError):
    def __init__(self):
        super().__init__(detail="Usuário não autenticado.")


class FailRefreshTokenError(AuthenticationError):
    def __init__(self):
        super().__init__(detail="Não foi possível atualizar o token.")


class LogoutFailKeycloakError(AuthenticationError):
    def __init__(self):
        super().__init__(detail="Falha ao realizar logout no Keycloak.")


class FailFetchJwkError(AuthenticationError):
    def __init__(self):
        super().__init__(detail="Falha ao buscar JWKs")


class TokenExchangeFailedError(AuthenticationError):
    def __init__(self, status_code: int, detail: str):
        super().__init__(detail=f"Falha ao trocar código por tokens: {detail}")
        self.status = status_code


class TokenVerificationFailedError(AuthenticationError):
    def __init__(self):
        super().__init__(detail="Falha na verificação do token.")


class UserIdNotFoundTokenError(AuthenticationError):
    def __init__(self):
        super().__init__(detail='ID do usuário não encontrado no token.')



class MissingRefreshTokenError(AuthenticationError):
    def __init__(self):
        super().__init__(detail="Refresh token ausente.")


class MissingAuthCallbackParameterError(AuthenticationError):
    def __init__(self, parameter: str):
        super().__init__(detail=f"Parâmetro obrigatório ausente no callback de autenticação: '{parameter}'.")

        

#endregion



#region ############## Erros de Validação ###############




class InvalidArgumentReportTypeError(ValidationError):
    def __init__(self):
        super().__init__(detail='É obrigatório enviar o campo "type" na requisição.')


class InvalidArgumentMissingIdError(ValidationError):
    def __init__(self):
        super().__init__(detail='É obrigatório informar o "reportId" para consultar um relatório.')


class InvalidArgumentMissingFieldError(ValidationError):
    def __init__(self, field: str):
        super().__init__(detail=f'É obrigatório informar o campo "{field}" nesta requisição.', errors=[{"field": field, "code": "CAMPO_OBRIGATORIO", "message": "Campo obrigatório."}])



class InvalidNumberOfReportsError(ValidationError):
    def __init__(self):
        super().__init__(detail="É necessário enviar pelo menos 2 report_ids para realizar a mesclagem.")


class InvalidReportIdError(ValidationError):
    def __init__(self, report_id: str):
        super().__init__(detail=f"Relatório com o ID {report_id} não existe.")


class InvalidReportTypesError(ValidationError):
    def __init__(self):
        super().__init__(detail="Só é possível mesclar relatórios de tipos diferentes.")


class MissingMainReportError(ValidationError):
    def __init__(self):
        super().__init__(detail="É necessário enviar um relatório de CPF ou CNPJ para a mesclagem.")


class FieldNotArrayError(ValidationError):
    def __init__(self, field: str):
        super().__init__(detail=f'O campo "{field}" deve ser enviado como uma lista (array).')


class MissingEditUserAccessError(ValidationError):
    def __init__(self):
        super().__init__(detail="Usuário não tem acesso para editar o usuário.")


class MissingSetApiKeyAccessError(ValidationError):
    def __init__(self):
        super().__init__(detail="Usuário não tem acesso para definir a chave de API.")


class FailAddOneToPendingError(ValidationError):
    def __init__(self):
        super().__init__(detail="Falha ao adicionar um relatório pendente ao usuário.")


class FailCreateEmptyReportError(ValidationError):
    def __init__(self):
        super().__init__(detail="Falha ao criar registro vazio.")


class FailUpdateErrorReportError(ValidationError):
    def __init__(self):
        super().__init__(detail="Falha ao atualizar relatório com erro.")


class FailToAccessUsersReportsError(ValidationError):
    def __init__(self):
        super().__init__(detail="Falha ao buscar relatórios do usuário.")


class FailToAccessDataToGetPendingReportsError(ValidationError):
    def __init__(self):
        super().__init__(detail="Falha ao acessar dados para buscar relatórios pendentes.")


class FailToDeleteReportError(ValidationError):
    def __init__(self):
        super().__init__(detail="Falha ao deletar relatório.")

class FailToDeleteHmacsError(ValidationError):
    def __init__(self):
        super().__init__(detail="Falha ao deletar hmac item.")

class FailToRenameReportError(ValidationError):
    def __init__(self):
        super().__init__(detail="Falha ao renomear relatório.")

class FailToAccessDataToGetNumberOfReportTypeError(ValidationError):
    def __init__(self):
        super().__init__(detail="Máximo de tentativas de obter dados da Snap API excedido.")


class InputValueSnapWrongError(ValidationError):
    def __init__(self):
        super().__init__(detail="Dados inseridos inválidos.")


class NoContextForClientError(ValidationError):
    def __init__(self, client_id: str):
        super().__init__(detail=f'Nenhum contexto encontrado para o client_id "{client_id}".', errors=[{"field": "client_id", "code": "CONTEXT_NOT_FOUND", "message": "Contexto não encontrado."}])


class RequestBodyTooLargeError(ValidationError):
    def __init__(self):
        super().__init__(detail="O corpo da requisição excede o tamanho máximo permitido.")


class FailToAccessDataToGetPendingReportsApiError(ValidationError):
    def __init__(self):
        super().__init__(detail="Falha ao acessar dados para buscar relatórios pendentes (API).")


class MissingAuthCodeError(ValidationError):
    def __init__(self):
        super().__init__(detail="Código de autorização ausente.")


class VerifierCannotBeUpdateError(ValidationError):
    def __init__(self, user_id: str):
        super().__init__(detail=f"Falha ao inserir/verificar o verificador para o usuário de ID {user_id}.")


class MaxRetriesExceededError(ValidationError):
    def __init__(self):
        super().__init__(detail="Máximo de tentativas de obter dados da Snap API excedido.")

# Convites (Invites)

class InvalidInviteTypeError(ValidationError):
    def __init__(self, invite_type: str):
        super().__init__(detail=f"Tipo de convite inválido: {invite_type}")


class InvalidReportRequestFormatError(ValidationError):
    def __init__(self, reason: str = "Formato de requisição inválido"):
        super().__init__(detail=reason)


class UserCreditsDateMissingError(ValidationError):
    def __init__(self):
        super().__init__(detail="Uma ou ambas as datas de créditos estão ausentes.")


class ApiCreditsFetchError(ValidationError):
    def __init__(self, status_code: int, detail: str):
        super().__init__(detail=f"Erro ao buscar créditos da API: {detail}")
        self.status = status_code




class ApiKeyValidationError(ValidationError):
    def __init__(self, detail: str):
        super().__init__(detail=f"Erro de validação da API Key: {detail}")


class InviteMissingFilterError(ValidationError):
    def __init__(self, message: str = None):
        detail = message or "Pelo menos um filtro obrigatório deve ser fornecido para buscar ou atualizar convite."
        super().__init__(detail=detail)


#endregion



#region ############## Erros de Não Encontrado ###############


class UserNotFoundError(NotFoundError):
    def __init__(self, user_id: str):
        super().__init__(resource=f'Usuário "{user_id}"')


class InviteNotFoundError(NotFoundError):
    def __init__(self, invite_id: str):
        super().__init__(resource=f'Convite "{invite_id}"')


class UserNoActiveOrganizationError(NotFoundError):
    def __init__(self, user_id: str):
        super().__init__(resource=f'Usuário "{user_id}" sem organização ativa')


class OrganizationNotFoundError(NotFoundError):
    def __init__(self, organization_id: str = None):
        resource = f'Organização "{organization_id}"' if organization_id else "Organização"
        super().__init__(resource=resource)


#endregion



#region ############## Erros de Detalhes do Problema ###############


class InsufficientCreditsError(ProblemDetail):
    def __init__(self, report_type: str):
        super().__init__(
            type_uri="https://api.example.com/errors/insufficient-credits",
            title="Créditos Insuficientes",
            status=402,
            detail=f'Usuário não possui créditos suficientes para o relatório do tipo "{report_type}".',
            report_type=report_type
        )


class ReportNotImplementedError(ProblemDetail):
    def __init__(self, report_type: str):
        super().__init__(
            type_uri="https://api.example.com/errors/not-implemented",
            title="Não Implementado",
            status=501,
            detail=f'Tipo de relatório "{report_type}" ainda não suportado.',
            report_type=report_type
        )


class SnapApiFailedError(ProblemDetail):
    def __init__(self, status_code: int):
        super().__init__(
            type_uri="https://api.example.com/errors/snap-api-error",
            title="Erro na Snap API",
            status=502,
            detail=f"Snap API retornou um código de status inesperado: {status_code}.",
            external_status=status_code
        )


class TimeoutError(ProblemDetail):
    def __init__(self):
        super().__init__(
            type_uri="https://api.example.com/errors/timeout",
            title="Tempo limite excedido",
            status=408,
            detail="Tempo limite excedido durante requisição à API externa."
        )


class UserLicenseExpiredError(ProblemDetail):
    def __init__(self):
        super().__init__(
            type_uri="https://api.example.com/errors/license-expired",
            title="Licença expirada",
            status=402,
            detail="Sua licença expirou, por favor entre em contato com o suporte."
        )


#endregion



#region ############## Erros de Negócio ###############


class ReportAlreadyCreatedError(BusinessLogicError):
    def __init__(self):
        super().__init__(detail="A geração do relatório já foi iniciada.")


class ReportDoesNotExistError(BusinessLogicError):
    def __init__(self):
        super().__init__(detail="Relatório em branco não existe para criação.")


#endregion



#region ############## Erros de Serviços Externos ###############


# Quota & Pendências
class SpendQuotaFailedError(InternalServerError):
    def __init__(self):
        super().__init__(detail="Falha ao consumir a cota devido a um erro interno.")


class FailToFetchDataFromSnapApiError(InternalServerError):
    def __init__(self):
        super().__init__(detail="Falha ao buscar dados na Snap API após múltiplas tentativas.")


class ProblemsWithSnapApiError(InternalServerError):
    def __init__(self):
        super().__init__(detail="Problemas com a Snap API, tente novamente mais tarde.")


class SnapApiNoIdError(InternalServerError):
    def __init__(self):
        super().__init__(detail="A Snap API não retornou um request ID.")


class SetApiKeyError(InternalServerError):
    def __init__(self):
        super().__init__(detail="Erro ao definir a chave de API.")


class InviteInternalError(InternalServerError):
    def __init__(self, action: str, reason: str):
        super().__init__(detail=f"Falha ao {action} convite: {reason}")


class DatabaseUnavailableError(InternalServerError):
    def __init__(self):
        super().__init__(detail="Não foi possível conectar ao banco de dados.")


class ApiServiceUnavailableError(InternalServerError):
    def __init__(self):
        super().__init__(detail="Serviço de API temporariamente indisponível.")


class ApiKeyDatabaseOperationError(InternalServerError):
    def __init__(self):
        super().__init__(detail="Falha na operação de banco de dados para API Key.")



class UpdateMonthlyUserCreditsError(InternalServerError):
    def __init__(self):
        super().__init__(detail="Erro ao atualizar créditos mensais do usuário.")


class ApiCreditsServiceUnavailableError(InternalServerError):
    def __init__(self):
        super().__init__(detail="O serviço externo de créditos da API está temporariamente indisponível após múltiplas tentativas.")


class InviteDatabaseOperationError(InternalServerError):
    def __init__(self, message: str = None):
        detail = f"Erro de banco de dados ao operar convite: {message}" if message else "Erro de banco de dados ao operar convite."
        super().__init__(detail=detail)


class MinioOperationError(InternalServerError):
    def __init__(self, operation: str, detail: str = None):
        msg = f"Erro ao executar '{operation}' no MinIO."
        if detail:
            msg += f" Detalhe: {detail}"
        super().__init__(detail=msg)


class OrganizationDatabaseOperationError(InternalServerError):
    def __init__(self, message: str = None):
        detail = f"Erro de banco de dados ao buscar organização: {message}" if message else "Erro de banco de dados ao buscar organização."
        super().__init__(detail=detail)


class OrganizationUsersDatabaseOperationError(InternalServerError):
    def __init__(self, message: str = None):
        detail = f"Erro de banco de dados ao operar usuário da organização: {message}" if message else "Erro de banco de dados ao operar usuário da organização."
        super().__init__(detail=detail)


class UserDatabaseOperationError(InternalServerError):
    def __init__(self, message: str = None):
        detail = f"Erro de banco de dados ao operar usuário: {message}" if message else "Erro de banco de dados ao operar usuário."
        super().__init__(detail=detail)


class DatabaseOperationFailedError(InternalServerError):
    def __init__(self, message: str = None):
        detail = f"Falha na operação de banco de dados após múltiplas tentativas: {message}" if message else "Falha na operação de banco de dados após múltiplas tentativas."
        super().__init__(detail=detail)


class SaltGenerationError(InternalServerError):
    def __init__(self, message: str = None):
        detail = f"Erro ao gerar salt: {message}" if message else "Erro ao gerar salt."
        super().__init__(detail=detail)


class KeycloakAdminInitializationError(InternalServerError):
    def __init__(self, message: str = None):
        detail = f"Erro ao inicializar o cliente Keycloak Admin: {message}" if message else "Erro ao inicializar o cliente Keycloak Admin."
        super().__init__(detail=detail)


class KeycloakAdminUnavailableError(InternalServerError):
    def __init__(self):
        super().__init__(detail="Cliente Keycloak Admin não está disponível.")


#endregion