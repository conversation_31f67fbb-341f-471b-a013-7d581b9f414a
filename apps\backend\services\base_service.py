# backend/services/base_service.py

from sqlalchemy.ext.asyncio import AsyncSession
import logging
logger = logging.getLogger(__name__)
class BaseService:
    """
    Uma classe base para serviços que fornece uma sessão de banco de dados.
    
    Atributos:
        db (AsyncSession): A sessão do banco de dados para ser usada nas operações.
    """


    def __init__(self, db: AsyncSession):
        """
        Inicializa o BaseService com uma sessão de banco de dados.
        
        Args:
            db (AsyncSession): A sessão assíncrona do banco de dados.
        """
        self.db = db
        self.user_id = None
        self.organization_id = None
        self.api_key = None
        self.api_credits= None
        self.api_next_reset_credits= None
        
        logger.info("[BaseService][__init__] Initialized BaseService with db session: %s", db)

    def set_user_id(self, user_id: str):
        self.user_id= user_id
        logger.info("[BaseService][set_user_id] Set user_id to: %s", user_id)

    def set_organization_id(self, organization_id: str):
        self.organization_id = organization_id
        logger.info("[BaseService][set_organization_id] Set organization_id to: %s", organization_id)

    def set_api_key(self, api_key: str):
        logger.info("[set_api_key] Setting API key for user: %s", self.user_id)
        logger.debug("[set_api_key] API key to set: %s", api_key)
        self.api_key=api_key
        logger.debug("[set_api_key] API key set successfully for user: %s", self.user_id)

    async def _apply_contextual_filters_to_query(self, query, is_user_scope: bool, model_class=None):
        """
        Applies contextual filters to a query based on the scope.
        This is a base implementation that can be overridden by specific services.

        :param query: The SQLAlchemy query object to modify.
        :param is_user_scope: If True, filters for the current user's context (personal or within their org).
                              If False, filters for the entire organization's context.
        :param model_class: The model class to use for filtering (e.g., UserReports, Folder)
        """
        logger.info(
            "[_apply_contextual_filters_to_query] Applying filters for user %s with scope: %s",
            self.user_id, "user" if is_user_scope else "organization"
        )
        
        if not model_class:
            logger.warning("[_apply_contextual_filters_to_query] No model_class provided, returning original query")
            return query
            
        if is_user_scope:
            # Personal scope: filter by user_id
            if hasattr(model_class, 'user_id'):
                query = query.where(model_class.user_id == self.user_id)
                logger.debug("[_apply_contextual_filters_to_query] Applied user_id filter: %s", self.user_id)
            elif hasattr(model_class, 'user_folder_id'):
                query = query.where(model_class.user_folder_id == self.user_id)
                logger.debug("[_apply_contextual_filters_to_query] Applied user_folder_id filter: %s", self.user_id)
            else:
                logger.warning("[_apply_contextual_filters_to_query] Model class %s has no user_id or user_folder_id attribute", model_class.__name__)
        else:
            # Organization scope: filter by organization_id.
            if not self.organization_id:
                logger.warning("[_apply_contextual_filters_to_query] Organization scope requested but no organization_id is set for the service.")
                from sqlalchemy import literal
                return query.where(literal(False)) # Return no results if org ID is missing
            logger.info("[_apply_contextual_filters_to_query] Applying organization scope filter for organization_id: %s", self.organization_id)
            if hasattr(model_class, 'organization_id'):
                query = query.where(model_class.organization_id == self.organization_id)
            else:
                logger.warning("[_apply_contextual_filters_to_query] Model class %s has no organization_id attribute", model_class.__name__)
        return query

        