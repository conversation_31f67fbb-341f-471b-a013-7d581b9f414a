# Build and distribution
dist/
build/
dist-examples-root
dev-dist

# Dependencies
node_modules/
__pycache__/
*.py[cod]
*$py.class
*.so

# Environment variables
.env
.env.production
.env.prod.dev
.venv
env/
venv/
ENV/
env.bak/
venv.bak/
myenv
.env.prod

# Logs and debugging
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
pip-log.txt
pip-delete-this-directory.txt

# Testing and coverage
test-results/
playwright-report/
blob-report/
playwright/.cache/
coverage/
.coverage
.coverage.*
htmlcov/
.tox/
.nox/
.pytest_cache/
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
cover/

# Cache files
.cache
.ruff_cache/
.mypy_cache/
.dmypy.json
dmypy.json
.pyre/
.pytype/
tsconfig.tsbuildinfo

# IDE specific
.idea/
.vscode/
.spyderproject
.spyproject
.ropeproject

# OS specific
.DS_Store

# Python specific
*.egg-info/
.installed.cfg
*.egg
MANIFEST
*.mo
*.pot
cython_debug/
.pypirc
.pdm.toml
.pdm-python
.pdm-build/
__pypackages__/

# Notes and documentation
notes/
docs/_build/

# Database
db.sqlite3
db.sqlite3-journal

venv312
launch.json
# Other Python-specific files that might be needed
# Uncomment if you need to ignore these:
#poetry.lock
#pdm.lock
#Pipfile.lock
#.python-version
realm.json

.devcontainer/