import type {
  ReportData,
  NewReportRequest,
  Ngrams,
  ReportPDFProps,
  RenameReportPayload,
} from "~/types/global";
import { REPORTS_CLIENT } from "../clients/reports.client";
import { ListItem, ListReportOutputDTO } from "root/domain/http/dto/report/list.report.output";
import { REPORT_CONSTANTS } from "~/helpers/constants";

interface FetchReportListParams {
  hmacFilters?: string[] | undefined;
  page?: number;
  limit?: number;
  folderId?: string | null;
}

export const fetchReportList = async ({
  hmacFilters,
  page = 1,
  limit = 50,
  folderId = null
}: FetchReportListParams = {}): Promise<ReadonlyArray<ListItem>> => {
  try {
    const filterParam = REPORT_CONSTANTS.report_list_params.hmac_filter;
    let url = '/get-saved-reports';
    const queryParams = [`page=${page}`, `limit=${limit}`];  // paginação

    if (folderId) {
      queryParams.push(`folder_id=${folderId}`);
    }

    // filtros de busca
    if (hmacFilters && hmacFilters.length > 0) {
      console.log("hmacFilters: ", hmacFilters);
      hmacFilters.forEach(filter => {
        queryParams.push(`${filterParam}=${encodeURIComponent(filter)}`);
      });
    }

    // combinar params
    if (queryParams.length > 0) {
      url = `${url}?${queryParams.join('&')}`;
    }

    console.log(`Fetching reports with URL: ${url}`);
    const response = await REPORTS_CLIENT.get<ListItem[]>(url);
    const dataList = new ListReportOutputDTO(response.data);
    return dataList.getAllItems();
  } catch (error) {
    console.error("Error fetching reports list data:", error);
    throw error;
  }
};

// TODO - criar domain/http/dto/detail.reports.output.ts DetailsReportsOutputDTO
export const fetchReportById = async (id: string) => {
  try {
    const response = await REPORTS_CLIENT.get(`/get-one-report/${id}`);
    const data = await response.data;
    const result = data
    console.log("result: ", result);
    return result;
  } catch (error) {
    console.error("Error fetching report:", error);
    throw error;
  }
};

// TODO - criar domain/http/dto/create.reports.output.ts CreateReportsOutputDTO
export const createNewReport = async (payload: NewReportRequest) => {
  try {
    const response = await REPORTS_CLIENT.post(`/get-data-from-snap-api`, payload);
    if (!response?.data) {
      console.log("createNewReport response: ", response)
    }
    const data = response?.data;
    console.log("createNewReport data: ", data)
    return data;
  } catch (error) {
    console.error("createNewReport Error creating new report:", error);
    throw error;
  }
};

// TODO - criar domain/http/dto/add.reports.output.ts AddReportsOutputDTO
export const addNewReport = async (report: ReportData, id: string) => {
  try {
    const response = await REPORTS_CLIENT.put(`/insert-report/${id}`, report);
    return response.data;
  } catch (error) {
    console.log("addNewReport Error adding new report:", error);

    throw error;
  }
};

// TODO - adicionar endpoint real para filtro
export const getSearchedReports = async (searchTerm: Ngrams) => {
  try {
    console.log("getSearchedReports searchTerm:", searchTerm);
    const response = await REPORTS_CLIENT.post(`/get-saved-reports`, searchTerm);
    const reportList = new ListReportOutputDTO(response.data).getReports();
    return reportList;
  } catch (error) {
    console.error("Error searching reports:", error);
    throw error;
  }
};

export const deleteReport = async (id: string) => {
  try {
    const response = await REPORTS_CLIENT.delete(`/delete-report/${id}`);
    return response.data;
  } catch (error) {
    console.error("Error deleting report:", error);
    throw error;
  }
};

export const generatePDF = async (props: ReportPDFProps) => {
  try {
    const response = await REPORTS_CLIENT.post('/create/pdf', props, {
      responseType: 'blob'
    });
    const pdfBlob = response.data;
    const url = URL.createObjectURL(pdfBlob as Blob);
    return url;
  } catch (error) {
    console.error("Error generating PDF:", error);
    throw error;
  }
}

export const renameReport = async (payload: RenameReportPayload) => {
  try {
    const response = await REPORTS_CLIENT.patch(`/rename-report/${payload[REPORT_CONSTANTS.new_report.report_id]}`, payload);
    return response.data;
  } catch (error) {
    console.error("Error renaming report:", error);
    throw error;
  }
};
