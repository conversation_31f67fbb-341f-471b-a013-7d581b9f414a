import logging

logger = logging.getLogger(__name__)

from fastapi import APIRouter

# Try importing the routers
try:
    from api.auth_routes import router as auth_router
    from api.report_routes import router as report_router
    from api.organization_routes import router as organization_router
    from api.invite_routes import router as invite_router
    from api.apikey_routes import router as api_key_router
    from api.administrador_routes import router as adm_router
    from api.folder_routes import router as folder_router
    from api.file_routes import router as file_router
    from api.user_routes import router as user_router
except ImportError as e:
    logging.error("Failed to import route modules: %s", e)
    raise



router = APIRouter()

logger.info("Registering route groups...")

# Helper function to safely register routes
def safe_include_router(router_to_include, prefix: str, tags: list[str]):
    try:
        router.include_router(router_to_include, prefix=prefix, tags=tags)
        logger.info("Successfully registered routes: %s under %s", tags[0], prefix)
    except Exception as e:
        logger.error("Failed to register %s routes under %s: %s", tags[0], prefix, e)
        raise

# Register all routers with error handling
safe_include_router(auth_router, prefix="/auth", tags=["Authentication"])
safe_include_router(report_router, prefix="/reports", tags=["Reports"])
safe_include_router(organization_router, prefix="/reports", tags=["Organizations"])
safe_include_router(invite_router, prefix="/reports", tags=["Invite"])
safe_include_router(api_key_router, prefix="/reports", tags=["ApiKey"])
safe_include_router(adm_router, prefix="/reports", tags=["Administrador"])
safe_include_router(folder_router, prefix="/reports", tags=["Folders"])
safe_include_router(file_router, prefix="/reports", tags=["File"])
safe_include_router(user_router, prefix="/reports", tags=["User"])

logger.info("All routers registered successfully.")
