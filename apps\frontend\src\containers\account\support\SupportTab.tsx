import { Text, Button } from '@snap/design-system'
import { AtSign, Clock, Copy, Mail } from 'lucide-react'
import { toast } from 'sonner';

const SupportTab = () => {
  const emails = [
    { title: 'Suporte Técnico', email: '<EMAIL>' },
  ];

  const handleCopyEmail = (email: string, e: React.MouseEvent) => {
    e.stopPropagation();
    navigator.clipboard.writeText(email);
    toast.success(`E-mail ${email} copiado para a área de transferência.`, { duration: 2000 });
  };

  const handleSendEmail = (email: string, e: React.MouseEvent) => {
    e.stopPropagation();
    window.open(`mailto:${email}`, '_blank');
  };

  return (
    <div className='relative flex flex-col gap-8 p-4 h-full'>
      <div className='description'>
        <Text
          variant="body-lg"
          className='font-semibold'
        >
          Aqui você encontra todo o suporte de que precisa para tirar o máximo proveito da sua experiência
        </Text>
        <Text
          variant="body-lg"
          className='font-semibold'
        >
          Nossos especialistas estão prontos para lhe atender com o máximo de prontidão.
        </Text>
      </div>

      <div className='flex items-center gap-4'>
        <div ><AtSign size={64} className='text-secondary' /></div>
        <div >
          <Text variant="body-md" className='text-accent font-bold pb-2'>Envie um e-mail</Text>
          <div className='content'>
            <Text variant="body-md">
              Utilize os e-mails abaixo para fazer contato conosco.
            </Text >
            <Text variant="body-md">
              Escolha a área de sua preferência e clique para copiar ou enviar um e-mail.
            </Text>
          </div>
        </div>
      </div>
      <div className='flex-1'>
        <div className='flex flex-wrap gap-4'>
          {emails.map((email, index) => (
            <div key={index} className='flex flex-col border border-border rounded-md p-8 min-w-80 max-w-md'>
              <Text variant="body-md" className='text-accent font-bold'>{email.title}</Text>
              <div className='flex items-center justify-between gap-4'>
                <Text variant="body-md" className='flex-1 min-w-0 truncate'>{email.email}</Text>
                <div className='flex gap-2 flex-shrink-0'>
                  <Button
                    variant="outline"
                    onClick={(e) => handleCopyEmail(email.email, e)}
                    title='Copiar e-mail'
                  >
                    <Copy size={16} />
                  </Button>
                  <Button
                    variant="outline"
                    onClick={(e) => handleSendEmail(email.email, e)}
                    title='Enviar e-mail'
                  >
                    <Mail size={16} />
                  </Button>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      <div className='flex items-center gap-3'>
        <Clock size={32} className='text-secondary' />
        <Text
          variant="body-md"
          className='text-secondary'
        >
          Nosso suporte técnico e comercial atende durante o horário comercial (08:00 às 18:00) durante todos os dias úteis.
        </Text>
      </div>
    </div>
  )
}

export default SupportTab